#!/usr/bin/env python3
"""
Startup script for the AI Trading Analysis System
"""
import os
import sys
import subprocess
from pathlib import Path

def check_dependencies():
    """Check if all required dependencies are installed"""
    try:
        import fastapi
        import uvicorn
        import pandas
        import numpy
        import yfinance
        import talib
        import sklearn
        import tensorflow
        import torch
        import xgboost
        import lightgbm
        print("✅ All dependencies are installed")
        return True
    except ImportError as e:
        print(f"❌ Missing dependency: {e}")
        print("Please run: pip install -r requirements.txt")
        return False

def check_env_file():
    """Check if .env file exists"""
    if not Path('.env').exists():
        print("⚠️  .env file not found")
        print("Please copy .env.example to .env and configure your API keys")
        return False
    print("✅ .env file found")
    return True

def create_directories():
    """Create necessary directories"""
    directories = ['data', 'logs', 'models/saved']
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
    print("✅ Directories created")

def run_tests():
    """Run basic tests"""
    print("🧪 Running basic tests...")
    try:
        # Import and test basic functionality
        from utils.data_collector import DataCollector
        from utils.company_validator import CompanyValidator
        
        # Test data collector
        data_collector = DataCollector()
        print("✅ Data collector initialized")
        
        # Test company validator
        validator = CompanyValidator()
        print("✅ Company validator initialized")
        
        print("✅ Basic tests passed")
        return True
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

def main():
    """Main startup function"""
    print("🚀 Starting AI Trading Analysis System")
    print("=" * 50)
    
    # Check dependencies
    if not check_dependencies():
        sys.exit(1)
    
    # Check environment file
    if not check_env_file():
        print("You can still run the system, but some features may not work without API keys")
    
    # Create directories
    create_directories()
    
    # Run basic tests
    if not run_tests():
        print("⚠️  Some tests failed, but continuing anyway...")
    
    print("=" * 50)
    print("🎯 System ready! Starting server...")
    print("🚀 Live Trading Dashboard: http://localhost:8000/dashboard")
    print("📊 API Documentation: http://localhost:8000/docs")
    print("🔍 Health Check: http://localhost:8000/health")
    print("📈 Market Scanner: http://localhost:8000/scan")
    print("💼 Portfolio Management: http://localhost:8000/portfolio")
    print("🔴 Live WebSocket: ws://localhost:8000/ws/live")
    print("=" * 50)
    
    # Start the server
    try:
        import uvicorn
        from main import app
        
        uvicorn.run(
            "main:app",
            host="0.0.0.0",
            port=8000,
            reload=True,
            log_level="info"
        )
    except KeyboardInterrupt:
        print("\n👋 Shutting down server...")
    except Exception as e:
        print(f"❌ Server error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
