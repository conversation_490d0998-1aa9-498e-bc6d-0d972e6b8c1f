"""
Configuration settings for the AI Trading Analysis System
"""
import os
from dotenv import load_dotenv

load_dotenv()

class Config:
    # API Keys (set these in your .env file)
    ALPHA_VANTAGE_API_KEY = os.getenv("ALPHA_VANTAGE_API_KEY")
    FINNHUB_API_KEY = os.getenv("FINNHUB_API_KEY")
    QUANDL_API_KEY = os.getenv("QUANDL_API_KEY")
    
    # Database settings
    DATABASE_URL = os.getenv("DATABASE_URL", "sqlite:///trading_data.db")
    
    # Model settings
    MODEL_RETRAIN_INTERVAL_HOURS = 24
    PREDICTION_CONFIDENCE_THRESHOLD = 0.7
    
    # Trading parameters
    DEFAULT_SYMBOLS = ["AAPL", "GOOGL", "MSFT", "TSLA", "AMZN", "NVDA", "META"]
    MAX_SYMBOLS_PER_REQUEST = 10
    
    # Technical analysis parameters
    RSI_PERIOD = 14
    MACD_FAST = 12
    MACD_SLOW = 26
    MACD_SIGNAL = 9
    BOLLINGER_PERIOD = 20
    BOLLINGER_STD = 2
    
    # Candlestick pattern settings
    PATTERN_LOOKBACK_DAYS = 30
    MIN_PATTERN_CONFIDENCE = 0.6
    
    # Company validation settings
    MIN_MARKET_CAP = 1000000000  # $1B minimum market cap
    MIN_TRADING_VOLUME = 100000  # Minimum daily volume
    
    # Logging
    LOG_LEVEL = "INFO"
    LOG_FILE = "trading_analysis.log"
    
    # Server settings
    HOST = "0.0.0.0"
    PORT = 8000
    DEBUG = os.getenv("DEBUG", "False").lower() == "true"
