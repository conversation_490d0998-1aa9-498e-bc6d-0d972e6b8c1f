"""
Live Trading Engine for real-time market analysis and trading signals
"""
import asyncio
import websockets
import json
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Callable
from loguru import logger
import yfinance as yf
from threading import Thread
import time
from collections import deque
import requests

from config import Config
from utils.technical_analysis import TechnicalAnalyzer
from utils.signal_generator import SignalGenerator

class LiveTradingEngine:
    def __init__(self):
        self.config = Config()
        self.technical_analyzer = TechnicalAnalyzer()
        self.signal_generator = SignalGenerator()
        
        # Live data storage
        self.live_data = {}  # {symbol: deque of recent prices}
        self.current_signals = {}  # {symbol: latest signal}
        self.price_alerts = {}  # {symbol: [alert_configs]}
        self.signal_history = {}  # {symbol: [historical_signals]}
        
        # WebSocket connections
        self.websocket_connections = set()
        self.is_running = False
        
        # Market hours
        self.market_open_time = "09:30"
        self.market_close_time = "16:00"
        
    async def start_live_monitoring(self, symbols: List[str]):
        """Start live monitoring for given symbols"""
        logger.info(f"Starting live monitoring for {len(symbols)} symbols")
        self.is_running = True
        
        # Initialize data storage
        for symbol in symbols:
            self.live_data[symbol] = deque(maxlen=1000)  # Keep last 1000 data points
            self.current_signals[symbol] = None
            self.signal_history[symbol] = deque(maxlen=100)
        
        # Start monitoring tasks
        tasks = [
            self.monitor_prices(symbols),
            self.generate_live_signals(symbols),
            self.check_alerts(symbols),
            self.broadcast_updates()
        ]
        
        await asyncio.gather(*tasks)
    
    async def monitor_prices(self, symbols: List[str]):
        """Monitor real-time prices for symbols"""
        while self.is_running:
            try:
                for symbol in symbols:
                    # Get current price data
                    ticker = yf.Ticker(symbol)
                    
                    # Get intraday data (last 2 days, 1-minute intervals)
                    hist = ticker.history(period="2d", interval="1m")
                    
                    if not hist.empty:
                        latest_data = hist.iloc[-1]
                        
                        price_data = {
                            'timestamp': datetime.now(),
                            'symbol': symbol,
                            'price': float(latest_data['Close']),
                            'volume': int(latest_data['Volume']),
                            'high': float(latest_data['High']),
                            'low': float(latest_data['Low']),
                            'open': float(latest_data['Open'])
                        }
                        
                        # Store in live data
                        self.live_data[symbol].append(price_data)
                        
                        # Log significant price movements
                        if len(self.live_data[symbol]) > 1:
                            prev_price = self.live_data[symbol][-2]['price']
                            price_change = (price_data['price'] - prev_price) / prev_price * 100
                            
                            if abs(price_change) > 1.0:  # Log moves > 1%
                                logger.info(f"{symbol}: {price_change:+.2f}% to ${price_data['price']:.2f}")
                
                # Wait before next update (respect rate limits)
                await asyncio.sleep(60)  # Update every minute
                
            except Exception as e:
                logger.error(f"Error monitoring prices: {str(e)}")
                await asyncio.sleep(30)
    
    async def generate_live_signals(self, symbols: List[str]):
        """Generate live trading signals"""
        while self.is_running:
            try:
                for symbol in symbols:
                    if symbol in self.live_data and len(self.live_data[symbol]) >= 50:
                        # Convert live data to DataFrame
                        df = self.convert_live_data_to_df(symbol)
                        
                        if len(df) >= 50:
                            # Generate signals
                            signals = self.signal_generator.generate_comprehensive_signals(symbol, df)
                            
                            # Check if signal changed
                            prev_signal = self.current_signals.get(symbol)
                            current_signal = signals.get('primary_signal')
                            
                            if prev_signal != current_signal:
                                logger.info(f"🚨 SIGNAL CHANGE: {symbol} {prev_signal} → {current_signal}")
                                
                                # Store signal
                                self.current_signals[symbol] = signals
                                self.signal_history[symbol].append({
                                    'timestamp': datetime.now(),
                                    'signal': current_signal,
                                    'confidence': signals.get('confidence', 0),
                                    'price': self.live_data[symbol][-1]['price']
                                })
                                
                                # Broadcast signal change
                                await self.broadcast_signal_change(symbol, signals)
                
                # Generate signals every 5 minutes
                await asyncio.sleep(300)
                
            except Exception as e:
                logger.error(f"Error generating live signals: {str(e)}")
                await asyncio.sleep(60)
    
    def convert_live_data_to_df(self, symbol: str) -> pd.DataFrame:
        """Convert live data to DataFrame for analysis"""
        data_list = list(self.live_data[symbol])
        
        if not data_list:
            return pd.DataFrame()
        
        df = pd.DataFrame([
            {
                'Date': item['timestamp'],
                'Open': item['open'],
                'High': item['high'],
                'Low': item['low'],
                'Close': item['price'],
                'Volume': item['volume']
            }
            for item in data_list
        ])
        
        df.set_index('Date', inplace=True)
        return df
    
    async def check_alerts(self, symbols: List[str]):
        """Check price alerts and notifications"""
        while self.is_running:
            try:
                for symbol in symbols:
                    if symbol in self.live_data and self.live_data[symbol]:
                        current_price = self.live_data[symbol][-1]['price']
                        
                        # Check price alerts
                        if symbol in self.price_alerts:
                            for alert in self.price_alerts[symbol]:
                                if self.check_alert_condition(current_price, alert):
                                    await self.trigger_alert(symbol, alert, current_price)
                
                await asyncio.sleep(30)  # Check alerts every 30 seconds
                
            except Exception as e:
                logger.error(f"Error checking alerts: {str(e)}")
                await asyncio.sleep(60)
    
    def check_alert_condition(self, current_price: float, alert: Dict) -> bool:
        """Check if alert condition is met"""
        condition = alert['condition']
        target_price = alert['price']
        
        if condition == 'above' and current_price > target_price:
            return True
        elif condition == 'below' and current_price < target_price:
            return True
        elif condition == 'change':
            # Check percentage change from alert creation
            initial_price = alert.get('initial_price', target_price)
            change_pct = abs((current_price - initial_price) / initial_price * 100)
            return change_pct >= target_price  # target_price is percentage in this case
        
        return False
    
    async def trigger_alert(self, symbol: str, alert: Dict, current_price: float):
        """Trigger price alert"""
        alert_message = {
            'type': 'price_alert',
            'symbol': symbol,
            'message': f"{symbol} price alert: ${current_price:.2f}",
            'alert_config': alert,
            'timestamp': datetime.now().isoformat()
        }
        
        logger.warning(f"🔔 PRICE ALERT: {alert_message['message']}")
        
        # Broadcast to connected clients
        await self.broadcast_to_clients(alert_message)
        
        # Remove triggered alert
        self.price_alerts[symbol] = [a for a in self.price_alerts[symbol] if a != alert]
    
    async def broadcast_updates(self):
        """Broadcast live updates to connected clients"""
        while self.is_running:
            try:
                if self.websocket_connections:
                    # Prepare market update
                    market_update = {
                        'type': 'market_update',
                        'timestamp': datetime.now().isoformat(),
                        'data': {}
                    }
                    
                    # Add current prices and signals
                    for symbol in self.live_data:
                        if self.live_data[symbol]:
                            latest_data = self.live_data[symbol][-1]
                            market_update['data'][symbol] = {
                                'price': latest_data['price'],
                                'volume': latest_data['volume'],
                                'timestamp': latest_data['timestamp'].isoformat(),
                                'signal': self.current_signals.get(symbol, {}).get('primary_signal', 'NEUTRAL')
                            }
                    
                    await self.broadcast_to_clients(market_update)
                
                await asyncio.sleep(10)  # Broadcast every 10 seconds
                
            except Exception as e:
                logger.error(f"Error broadcasting updates: {str(e)}")
                await asyncio.sleep(30)
    
    async def broadcast_to_clients(self, message: Dict):
        """Broadcast message to all connected WebSocket clients"""
        if self.websocket_connections:
            disconnected = set()
            
            for websocket in self.websocket_connections:
                try:
                    await websocket.send(json.dumps(message))
                except Exception:
                    disconnected.add(websocket)
            
            # Remove disconnected clients
            self.websocket_connections -= disconnected
    
    async def broadcast_signal_change(self, symbol: str, signals: Dict):
        """Broadcast signal change to clients"""
        signal_message = {
            'type': 'signal_change',
            'symbol': symbol,
            'signal': signals.get('primary_signal'),
            'confidence': signals.get('confidence'),
            'reasoning': signals.get('reasoning', []),
            'timestamp': datetime.now().isoformat()
        }
        
        await self.broadcast_to_clients(signal_message)
    
    def add_price_alert(self, symbol: str, condition: str, price: float, message: str = ""):
        """Add price alert for a symbol"""
        if symbol not in self.price_alerts:
            self.price_alerts[symbol] = []
        
        alert = {
            'condition': condition,  # 'above', 'below', 'change'
            'price': price,
            'message': message,
            'created_at': datetime.now(),
            'initial_price': self.live_data[symbol][-1]['price'] if symbol in self.live_data and self.live_data[symbol] else price
        }
        
        self.price_alerts[symbol].append(alert)
        logger.info(f"Added price alert for {symbol}: {condition} ${price:.2f}")
    
    def get_live_data(self, symbol: str, minutes: int = 60) -> List[Dict]:
        """Get recent live data for a symbol"""
        if symbol not in self.live_data:
            return []
        
        cutoff_time = datetime.now() - timedelta(minutes=minutes)
        recent_data = [
            data for data in self.live_data[symbol]
            if data['timestamp'] >= cutoff_time
        ]
        
        return recent_data
    
    def get_current_signals(self) -> Dict:
        """Get current signals for all monitored symbols"""
        return self.current_signals.copy()
    
    def get_signal_history(self, symbol: str) -> List[Dict]:
        """Get signal history for a symbol"""
        if symbol not in self.signal_history:
            return []
        
        return list(self.signal_history[symbol])
    
    def is_market_open(self) -> bool:
        """Check if market is currently open"""
        now = datetime.now()
        current_time = now.strftime("%H:%M")
        
        # Simple check - can be enhanced with market calendar
        if now.weekday() >= 5:  # Weekend
            return False
        
        return self.market_open_time <= current_time <= self.market_close_time
    
    def get_market_status(self) -> Dict:
        """Get current market status"""
        is_open = self.is_market_open()
        now = datetime.now()
        
        return {
            'is_open': is_open,
            'current_time': now.isoformat(),
            'status': 'OPEN' if is_open else 'CLOSED',
            'next_open': self.get_next_market_open().isoformat() if not is_open else None
        }
    
    def get_next_market_open(self) -> datetime:
        """Get next market open time"""
        now = datetime.now()
        
        # If it's weekend, next open is Monday
        if now.weekday() >= 5:
            days_until_monday = 7 - now.weekday()
            next_open = now + timedelta(days=days_until_monday)
            return next_open.replace(hour=9, minute=30, second=0, microsecond=0)
        
        # If market is closed today, next open is tomorrow (or Monday if Friday)
        if now.strftime("%H:%M") > self.market_close_time:
            if now.weekday() == 4:  # Friday
                next_open = now + timedelta(days=3)  # Monday
            else:
                next_open = now + timedelta(days=1)  # Tomorrow
            return next_open.replace(hour=9, minute=30, second=0, microsecond=0)
        
        # Market opens today
        return now.replace(hour=9, minute=30, second=0, microsecond=0)
    
    async def handle_websocket_connection(self, websocket, path):
        """Handle WebSocket connection for live updates"""
        self.websocket_connections.add(websocket)
        logger.info(f"New WebSocket connection: {len(self.websocket_connections)} total")
        
        try:
            # Send initial data
            initial_data = {
                'type': 'connection_established',
                'market_status': self.get_market_status(),
                'current_signals': self.get_current_signals(),
                'timestamp': datetime.now().isoformat()
            }
            await websocket.send(json.dumps(initial_data))
            
            # Keep connection alive
            async for message in websocket:
                # Handle incoming messages (e.g., alert requests)
                try:
                    data = json.loads(message)
                    await self.handle_client_message(websocket, data)
                except json.JSONDecodeError:
                    logger.warning("Received invalid JSON from client")
                    
        except Exception as e:
            logger.error(f"WebSocket error: {str(e)}")
        finally:
            self.websocket_connections.discard(websocket)
            logger.info(f"WebSocket disconnected: {len(self.websocket_connections)} remaining")
    
    async def handle_client_message(self, websocket, data: Dict):
        """Handle messages from WebSocket clients"""
        message_type = data.get('type')
        
        if message_type == 'add_alert':
            symbol = data.get('symbol')
            condition = data.get('condition')
            price = data.get('price')
            
            if symbol and condition and price:
                self.add_price_alert(symbol, condition, float(price))
                
                response = {
                    'type': 'alert_added',
                    'symbol': symbol,
                    'message': f"Alert added for {symbol}",
                    'timestamp': datetime.now().isoformat()
                }
                await websocket.send(json.dumps(response))
        
        elif message_type == 'get_history':
            symbol = data.get('symbol')
            if symbol:
                history = self.get_signal_history(symbol)
                response = {
                    'type': 'signal_history',
                    'symbol': symbol,
                    'history': history,
                    'timestamp': datetime.now().isoformat()
                }
                await websocket.send(json.dumps(response))
    
    def stop_monitoring(self):
        """Stop live monitoring"""
        logger.info("Stopping live trading engine")
        self.is_running = False
