"""
Comprehensive tests for the AI Trading Analysis System
"""
import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
import os

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.data_collector import DataCollector
from utils.company_validator import CompanyValidator
from utils.technical_analysis import TechnicalAnalyzer
from utils.timeframe_analyzer import TimeframeAnalyzer
from utils.signal_generator import SignalGenerator
from models.ai_predictor import AIPredictor

class TestDataCollector:
    def setup_method(self):
        self.data_collector = DataCollector()
    
    def test_get_historical_data(self):
        """Test historical data collection"""
        # Test with a known symbol
        df = self.data_collector.get_historical_data("AAPL", period="1mo", interval="1d")
        
        assert not df.empty, "Historical data should not be empty"
        assert 'Open' in df.columns, "DataFrame should have Open column"
        assert 'High' in df.columns, "DataFrame should have High column"
        assert 'Low' in df.columns, "DataFrame should have Low column"
        assert 'Close' in df.columns, "DataFrame should have Close column"
        assert 'Volume' in df.columns, "DataFrame should have Volume column"
        assert len(df) > 0, "Should have at least some data points"
    
    def test_get_real_time_data(self):
        """Test real-time data collection"""
        data = self.data_collector.get_real_time_data("AAPL")
        
        assert isinstance(data, dict), "Real-time data should be a dictionary"
        assert 'symbol' in data, "Should contain symbol"
        assert 'current_price' in data, "Should contain current price"
    
    def test_validate_symbol(self):
        """Test symbol validation"""
        # Test valid symbol
        assert self.data_collector.validate_symbol("AAPL") == True
        
        # Test invalid symbol
        assert self.data_collector.validate_symbol("INVALID123") == False

class TestCompanyValidator:
    def setup_method(self):
        self.validator = CompanyValidator()
    
    def test_validate_company_legitimacy(self):
        """Test company legitimacy validation"""
        result = self.validator.validate_company_legitimacy("AAPL")
        
        assert isinstance(result, dict), "Result should be a dictionary"
        assert 'symbol' in result, "Should contain symbol"
        assert 'is_legitimate' in result, "Should contain legitimacy flag"
        assert 'legitimacy_score' in result, "Should contain legitimacy score"
        assert 0 <= result['legitimacy_score'] <= 1, "Score should be between 0 and 1"
    
    def test_check_penny_stock(self):
        """Test penny stock detection"""
        # AAPL should not be a penny stock
        assert self.validator.check_penny_stock("AAPL") == False
    
    def test_get_risk_assessment(self):
        """Test risk assessment"""
        risk = self.validator.get_risk_assessment("AAPL")
        
        assert isinstance(risk, dict), "Risk assessment should be a dictionary"
        assert 'risk_level' in risk, "Should contain risk level"
        assert 'legitimacy_score' in risk, "Should contain legitimacy score"

class TestTechnicalAnalyzer:
    def setup_method(self):
        self.analyzer = TechnicalAnalyzer()
        # Create sample data
        dates = pd.date_range(start='2023-01-01', end='2023-12-31', freq='D')
        np.random.seed(42)
        prices = 100 + np.cumsum(np.random.randn(len(dates)) * 0.5)
        
        self.sample_df = pd.DataFrame({
            'Date': dates,
            'Open': prices + np.random.randn(len(dates)) * 0.1,
            'High': prices + np.abs(np.random.randn(len(dates)) * 0.5),
            'Low': prices - np.abs(np.random.randn(len(dates)) * 0.5),
            'Close': prices,
            'Volume': np.random.randint(1000000, 10000000, len(dates))
        })
    
    def test_calculate_indicators(self):
        """Test technical indicators calculation"""
        df_with_indicators = self.analyzer.calculate_indicators(self.sample_df)
        
        # Check if indicators are added
        assert 'RSI' in df_with_indicators.columns, "Should have RSI"
        assert 'MACD' in df_with_indicators.columns, "Should have MACD"
        assert 'SMA_20' in df_with_indicators.columns, "Should have SMA_20"
        assert 'BB_Upper' in df_with_indicators.columns, "Should have Bollinger Bands"
        
        # Check if values are reasonable
        rsi_values = df_with_indicators['RSI'].dropna()
        assert all(0 <= val <= 100 for val in rsi_values), "RSI should be between 0 and 100"
    
    def test_detect_candlestick_patterns(self):
        """Test candlestick pattern detection"""
        df_with_patterns = self.analyzer.detect_candlestick_patterns(self.sample_df)
        
        # Check if pattern columns are added
        assert 'Hammer' in df_with_patterns.columns, "Should have Hammer pattern"
        assert 'Doji' in df_with_patterns.columns, "Should have Doji pattern"
        assert 'Bullish_Engulfing' in df_with_patterns.columns, "Should have Bullish Engulfing pattern"
    
    def test_analyze_trend(self):
        """Test trend analysis"""
        df_with_indicators = self.analyzer.calculate_indicators(self.sample_df)
        trend_analysis = self.analyzer.analyze_trend(df_with_indicators)
        
        assert isinstance(trend_analysis, dict), "Trend analysis should be a dictionary"
        assert 'overall_trend' in trend_analysis, "Should contain overall trend"
        assert trend_analysis['overall_trend'] in ['BULLISH', 'BEARISH', 'NEUTRAL'], "Trend should be valid"

class TestAIPredictor:
    def setup_method(self):
        self.predictor = AIPredictor()
        # Create sample data with more realistic structure
        dates = pd.date_range(start='2022-01-01', end='2023-12-31', freq='D')
        np.random.seed(42)
        prices = 100 + np.cumsum(np.random.randn(len(dates)) * 0.5)
        
        self.sample_df = pd.DataFrame({
            'Date': dates,
            'Open': prices + np.random.randn(len(dates)) * 0.1,
            'High': prices + np.abs(np.random.randn(len(dates)) * 0.5),
            'Low': prices - np.abs(np.random.randn(len(dates)) * 0.5),
            'Close': prices,
            'Volume': np.random.randint(1000000, 10000000, len(dates))
        })
    
    def test_prepare_features(self):
        """Test feature preparation"""
        df_features = self.predictor.prepare_features(self.sample_df)
        
        assert 'Price_Change' in df_features.columns, "Should have price change feature"
        assert 'Volume_Change' in df_features.columns, "Should have volume change feature"
        assert 'Target_1d' in df_features.columns, "Should have target variable"
    
    def test_train_regression_models(self):
        """Test model training"""
        df_features = self.predictor.prepare_features(self.sample_df)
        results = self.predictor.train_regression_models(df_features)
        
        if results:  # Only test if training was successful
            assert isinstance(results, dict), "Results should be a dictionary"
            for model_name, metrics in results.items():
                assert 'mse' in metrics, f"{model_name} should have MSE metric"
                assert 'r2' in metrics, f"{model_name} should have R2 metric"
    
    def test_predict_trend_direction(self):
        """Test trend direction prediction"""
        df_features = self.predictor.prepare_features(self.sample_df)
        prediction = self.predictor.predict_trend_direction(df_features)
        
        assert isinstance(prediction, dict), "Prediction should be a dictionary"
        assert 'prediction' in prediction, "Should contain prediction"
        assert 'confidence' in prediction, "Should contain confidence"

class TestSignalGenerator:
    def setup_method(self):
        self.signal_generator = SignalGenerator()
        # Create sample data
        dates = pd.date_range(start='2023-01-01', end='2023-12-31', freq='D')
        np.random.seed(42)
        prices = 100 + np.cumsum(np.random.randn(len(dates)) * 0.5)
        
        self.sample_df = pd.DataFrame({
            'Date': dates,
            'Open': prices + np.random.randn(len(dates)) * 0.1,
            'High': prices + np.abs(np.random.randn(len(dates)) * 0.5),
            'Low': prices - np.abs(np.random.randn(len(dates)) * 0.5),
            'Close': prices,
            'Volume': np.random.randint(1000000, 10000000, len(dates))
        })
    
    def test_generate_comprehensive_signals(self):
        """Test comprehensive signal generation"""
        # This test might take longer due to company validation
        signals = self.signal_generator.generate_comprehensive_signals("AAPL", self.sample_df)
        
        assert isinstance(signals, dict), "Signals should be a dictionary"
        assert 'symbol' in signals, "Should contain symbol"
        assert 'primary_signal' in signals, "Should contain primary signal"
        assert 'confidence' in signals, "Should contain confidence"
        assert 'risk_level' in signals, "Should contain risk level"

class TestIntegration:
    """Integration tests for the complete system"""
    
    def test_end_to_end_analysis(self):
        """Test complete analysis pipeline"""
        # Initialize components
        data_collector = DataCollector()
        signal_generator = SignalGenerator()
        
        # Get real data for a known symbol
        df = data_collector.get_historical_data("AAPL", period="6mo", interval="1d")
        
        if not df.empty:
            # Generate signals
            signals = signal_generator.generate_comprehensive_signals("AAPL", df)
            
            # Verify the complete pipeline works
            assert isinstance(signals, dict), "Should return signals dictionary"
            assert 'primary_signal' in signals, "Should have primary signal"
            assert signals['primary_signal'] in [
                'BUY', 'SELL', 'HOLD', 'STRONG_BUY', 'STRONG_SELL', 
                'CAUTIOUS_BUY', 'CAUTIOUS_SELL', 'AVOID', 'ERROR'
            ], "Signal should be valid"

def run_performance_test():
    """Performance test for the system"""
    import time
    
    print("Running performance tests...")
    
    # Test data collection speed
    start_time = time.time()
    data_collector = DataCollector()
    df = data_collector.get_historical_data("AAPL", period="1y", interval="1d")
    data_time = time.time() - start_time
    print(f"Data collection took: {data_time:.2f} seconds")
    
    # Test signal generation speed
    start_time = time.time()
    signal_generator = SignalGenerator()
    signals = signal_generator.generate_comprehensive_signals("AAPL", df)
    signal_time = time.time() - start_time
    print(f"Signal generation took: {signal_time:.2f} seconds")
    
    print(f"Total analysis time: {data_time + signal_time:.2f} seconds")

if __name__ == "__main__":
    # Run performance test
    run_performance_test()
    
    # Run pytest
    pytest.main([__file__, "-v"])
