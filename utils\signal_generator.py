"""
Trading Signal Generation based on AI predictions and technical analysis
"""
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
from loguru import logger
from utils.technical_analysis import TechnicalAnalyzer
from utils.timeframe_analyzer import TimeframeAnalyzer
from utils.company_validator import CompanyValidator
from config import Config

class SignalGenerator:
    def __init__(self):
        self.config = Config()
        self.technical_analyzer = TechnicalAnalyzer()
        self.timeframe_analyzer = TimeframeAnalyzer()
        self.company_validator = CompanyValidator()
        
    def generate_comprehensive_signals(self, symbol: str, df: pd.DataFrame) -> Dict:
        """
        Generate comprehensive trading signals combining all analysis methods
        
        Args:
            symbol: Stock symbol
            df: DataFrame with historical price data
            
        Returns:
            Dictionary with comprehensive trading signals
        """
        try:
            logger.info(f"Generating comprehensive signals for {symbol}")
            
            # Validate company legitimacy first
            company_validation = self.company_validator.validate_company_legitimacy(symbol)
            
            if not company_validation.get('is_legitimate', False):
                return {
                    'symbol': symbol,
                    'timestamp': datetime.now(),
                    'primary_signal': 'AVOID',
                    'confidence': 0.0,
                    'reasoning': ['Company failed legitimacy validation'],
                    'risk_level': 'VERY_HIGH',
                    'company_validation': company_validation
                }
            
            # Perform technical analysis
            df_with_indicators = self.technical_analyzer.calculate_indicators(df)
            df_with_patterns = self.technical_analyzer.detect_candlestick_patterns(df_with_indicators)
            
            # Get technical signals
            technical_signals = self._generate_technical_signals(df_with_patterns)
            
            # Get temporal analysis
            temporal_analysis = self.timeframe_analyzer.analyze_temporal_trends(df_with_patterns, symbol)
            
            # Get AI-based signals
            ai_signals = self._generate_ai_signals(df_with_patterns, symbol)
            
            # Combine all signals
            combined_signals = self._combine_signals(technical_signals, temporal_analysis, ai_signals)
            
            # Add risk assessment
            risk_assessment = self._assess_overall_risk(symbol, df_with_patterns, combined_signals, company_validation)
            
            # Generate final recommendation
            final_recommendation = self._generate_final_recommendation(combined_signals, risk_assessment)
            
            return {
                'symbol': symbol,
                'timestamp': datetime.now(),
                'primary_signal': final_recommendation['signal'],
                'confidence': final_recommendation['confidence'],
                'reasoning': final_recommendation['reasoning'],
                'risk_level': risk_assessment['risk_level'],
                'technical_signals': technical_signals,
                'temporal_analysis': temporal_analysis,
                'ai_signals': ai_signals,
                'combined_signals': combined_signals,
                'risk_assessment': risk_assessment,
                'company_validation': company_validation,
                'entry_price': df['Close'].iloc[-1],
                'stop_loss': self._calculate_stop_loss(df, final_recommendation['signal']),
                'take_profit': self._calculate_take_profit(df, final_recommendation['signal']),
                'position_size_recommendation': self._recommend_position_size(risk_assessment)
            }
            
        except Exception as e:
            logger.error(f"Error generating comprehensive signals for {symbol}: {str(e)}")
            return {
                'symbol': symbol,
                'timestamp': datetime.now(),
                'primary_signal': 'ERROR',
                'confidence': 0.0,
                'reasoning': [f'Analysis failed: {str(e)}'],
                'risk_level': 'UNKNOWN'
            }
    
    def _generate_technical_signals(self, df: pd.DataFrame) -> Dict:
        """Generate signals based on technical analysis"""
        try:
            signals = {
                'trend_signals': [],
                'momentum_signals': [],
                'pattern_signals': [],
                'volume_signals': [],
                'overall_technical_signal': 'NEUTRAL',
                'technical_confidence': 0.5
            }
            
            latest = df.iloc[-1]
            
            # Trend signals
            if latest['Close'] > latest['SMA_20'] > latest['SMA_50']:
                signals['trend_signals'].append('BULLISH_TREND')
            elif latest['Close'] < latest['SMA_20'] < latest['SMA_50']:
                signals['trend_signals'].append('BEARISH_TREND')
            
            # RSI signals
            rsi = latest['RSI']
            if rsi > 70:
                signals['momentum_signals'].append('OVERBOUGHT')
            elif rsi < 30:
                signals['momentum_signals'].append('OVERSOLD')
            elif 40 <= rsi <= 60:
                signals['momentum_signals'].append('NEUTRAL_MOMENTUM')
            
            # MACD signals
            if latest['MACD'] > latest['MACD_Signal']:
                signals['momentum_signals'].append('MACD_BULLISH')
            else:
                signals['momentum_signals'].append('MACD_BEARISH')
            
            # Bollinger Bands signals
            if latest['Close'] > latest['BB_Upper']:
                signals['momentum_signals'].append('BB_BREAKOUT_UP')
            elif latest['Close'] < latest['BB_Lower']:
                signals['momentum_signals'].append('BB_BREAKOUT_DOWN')
            
            # Pattern signals
            pattern_summary = self.technical_analyzer.get_pattern_summary(df)
            if pattern_summary['bullish_patterns'] > pattern_summary['bearish_patterns']:
                signals['pattern_signals'].append('BULLISH_PATTERNS')
            elif pattern_summary['bearish_patterns'] > pattern_summary['bullish_patterns']:
                signals['pattern_signals'].append('BEARISH_PATTERNS')
            
            # Volume signals
            if 'Volume_SMA' in df.columns:
                volume_ratio = latest['Volume'] / latest['Volume_SMA']
                if volume_ratio > 1.5:
                    signals['volume_signals'].append('HIGH_VOLUME')
                elif volume_ratio < 0.5:
                    signals['volume_signals'].append('LOW_VOLUME')
            
            # Calculate overall technical signal
            bullish_signals = len([s for s in signals['trend_signals'] + signals['momentum_signals'] + 
                                 signals['pattern_signals'] if 'BULLISH' in s or 'UP' in s])
            bearish_signals = len([s for s in signals['trend_signals'] + signals['momentum_signals'] + 
                                 signals['pattern_signals'] if 'BEARISH' in s or 'DOWN' in s])
            
            if bullish_signals > bearish_signals:
                signals['overall_technical_signal'] = 'BUY'
                signals['technical_confidence'] = bullish_signals / (bullish_signals + bearish_signals)
            elif bearish_signals > bullish_signals:
                signals['overall_technical_signal'] = 'SELL'
                signals['technical_confidence'] = bearish_signals / (bullish_signals + bearish_signals)
            
            return signals
            
        except Exception as e:
            logger.error(f"Error generating technical signals: {str(e)}")
            return {'overall_technical_signal': 'NEUTRAL', 'technical_confidence': 0.5}
    
    def _generate_ai_signals(self, df: pd.DataFrame, symbol: str) -> Dict:
        """Generate signals based on AI predictions"""
        try:
            from models.ai_predictor import AIPredictor
            
            ai_predictor = AIPredictor()
            
            # Train models if needed
            if not hasattr(ai_predictor, 'models') or not ai_predictor.models:
                ai_predictor.train_regression_models(df)
            
            # Get predictions
            price_predictions = ai_predictor.predict_price(df, days_ahead=1)
            trend_predictions = ai_predictor.predict_trend_direction(df)
            
            current_price = df['Close'].iloc[-1]
            
            signals = {
                'price_predictions': price_predictions,
                'trend_predictions': trend_predictions,
                'ai_signal': 'NEUTRAL',
                'ai_confidence': 0.5,
                'predicted_return': 0.0
            }
            
            # Calculate predicted return
            if 'Ensemble' in price_predictions:
                predicted_price = price_predictions['Ensemble']
                predicted_return = (predicted_price - current_price) / current_price * 100
                signals['predicted_return'] = predicted_return
                
                # Generate signal based on predicted return
                if predicted_return > 2:
                    signals['ai_signal'] = 'STRONG_BUY'
                elif predicted_return > 0.5:
                    signals['ai_signal'] = 'BUY'
                elif predicted_return < -2:
                    signals['ai_signal'] = 'STRONG_SELL'
                elif predicted_return < -0.5:
                    signals['ai_signal'] = 'SELL'
            
            # Use trend prediction confidence
            signals['ai_confidence'] = trend_predictions.get('confidence', 0.5)
            
            return signals
            
        except Exception as e:
            logger.error(f"Error generating AI signals: {str(e)}")
            return {'ai_signal': 'NEUTRAL', 'ai_confidence': 0.5}
    
    def _combine_signals(self, technical_signals: Dict, temporal_analysis: Dict, ai_signals: Dict) -> Dict:
        """Combine all signals into a unified assessment"""
        try:
            combined = {
                'signal_sources': {
                    'technical': technical_signals.get('overall_technical_signal', 'NEUTRAL'),
                    'temporal': temporal_analysis.get('overall_assessment', {}).get('overall_trend', 'NEUTRAL'),
                    'ai': ai_signals.get('ai_signal', 'NEUTRAL')
                },
                'confidence_scores': {
                    'technical': technical_signals.get('technical_confidence', 0.5),
                    'temporal': temporal_analysis.get('overall_assessment', {}).get('trend_consistency', 0.5),
                    'ai': ai_signals.get('ai_confidence', 0.5)
                },
                'signal_agreement': 0.0,
                'combined_signal': 'NEUTRAL',
                'combined_confidence': 0.5
            }
            
            # Calculate signal agreement
            signals = list(combined['signal_sources'].values())
            buy_signals = len([s for s in signals if 'BUY' in s or s == 'BULLISH'])
            sell_signals = len([s for s in signals if 'SELL' in s or s == 'BEARISH'])
            neutral_signals = len([s for s in signals if s == 'NEUTRAL'])
            
            total_signals = len(signals)
            if buy_signals > sell_signals and buy_signals > neutral_signals:
                combined['combined_signal'] = 'BUY'
                combined['signal_agreement'] = buy_signals / total_signals
            elif sell_signals > buy_signals and sell_signals > neutral_signals:
                combined['combined_signal'] = 'SELL'
                combined['signal_agreement'] = sell_signals / total_signals
            else:
                combined['combined_signal'] = 'NEUTRAL'
                combined['signal_agreement'] = max(buy_signals, sell_signals, neutral_signals) / total_signals
            
            # Calculate combined confidence
            confidences = list(combined['confidence_scores'].values())
            combined['combined_confidence'] = np.mean(confidences) * combined['signal_agreement']
            
            return combined
            
        except Exception as e:
            logger.error(f"Error combining signals: {str(e)}")
            return {'combined_signal': 'NEUTRAL', 'combined_confidence': 0.5}
    
    def _assess_overall_risk(self, symbol: str, df: pd.DataFrame, signals: Dict, company_validation: Dict) -> Dict:
        """Assess overall risk for the trading decision"""
        try:
            risk_factors = []
            risk_score = 0.5  # Start with neutral risk
            
            # Company legitimacy risk
            legitimacy_score = company_validation.get('legitimacy_score', 0.5)
            if legitimacy_score < 0.7:
                risk_factors.append("Company legitimacy concerns")
                risk_score += 0.2
            
            # Volatility risk
            volatility = self.technical_analyzer.calculate_volatility(df)
            hist_vol = volatility.get('historical_volatility', 0.02)
            if hist_vol > 0.3:
                risk_factors.append("High volatility")
                risk_score += 0.2
            elif hist_vol > 0.2:
                risk_factors.append("Moderate volatility")
                risk_score += 0.1
            
            # Signal confidence risk
            combined_confidence = signals.get('combined_confidence', 0.5)
            if combined_confidence < 0.6:
                risk_factors.append("Low signal confidence")
                risk_score += 0.15
            
            # Market cap risk (penny stocks)
            if self.company_validator.check_penny_stock(symbol):
                risk_factors.append("Penny stock - high risk")
                risk_score += 0.3
            
            # Volume risk
            latest = df.iloc[-1]
            if 'Volume_SMA' in df.columns:
                volume_ratio = latest['Volume'] / latest['Volume_SMA']
                if volume_ratio < 0.3:
                    risk_factors.append("Low trading volume")
                    risk_score += 0.1
            
            # Determine risk level
            risk_score = min(risk_score, 1.0)
            if risk_score > 0.8:
                risk_level = "VERY_HIGH"
            elif risk_score > 0.6:
                risk_level = "HIGH"
            elif risk_score > 0.4:
                risk_level = "MEDIUM"
            else:
                risk_level = "LOW"
            
            return {
                'risk_level': risk_level,
                'risk_score': risk_score,
                'risk_factors': risk_factors,
                'volatility_metrics': volatility
            }
            
        except Exception as e:
            logger.error(f"Error assessing overall risk: {str(e)}")
            return {'risk_level': 'UNKNOWN', 'risk_score': 0.5, 'risk_factors': []}
    
    def _generate_final_recommendation(self, signals: Dict, risk_assessment: Dict) -> Dict:
        """Generate final trading recommendation"""
        try:
            combined_signal = signals.get('combined_signal', 'NEUTRAL')
            combined_confidence = signals.get('combined_confidence', 0.5)
            risk_level = risk_assessment.get('risk_level', 'MEDIUM')
            
            reasoning = []
            
            # Adjust signal based on risk
            if risk_level in ['VERY_HIGH', 'HIGH']:
                if combined_signal in ['BUY', 'STRONG_BUY']:
                    combined_signal = 'CAUTIOUS_BUY'
                    reasoning.append("Signal downgraded due to high risk")
                elif combined_signal in ['SELL', 'STRONG_SELL']:
                    combined_signal = 'STRONG_SELL'
                    reasoning.append("Sell signal reinforced by high risk")
                else:
                    combined_signal = 'AVOID'
                    reasoning.append("Avoiding due to high risk")
                
                combined_confidence *= 0.7  # Reduce confidence for high-risk stocks
            
            # Add reasoning based on signal sources
            signal_sources = signals.get('signal_sources', {})
            agreement_count = len([s for s in signal_sources.values() if s == combined_signal or 
                                 (combined_signal == 'BUY' and 'BUY' in s) or 
                                 (combined_signal == 'SELL' and 'SELL' in s)])
            
            if agreement_count >= 2:
                reasoning.append(f"Multiple analysis methods agree ({agreement_count}/3)")
            else:
                reasoning.append("Mixed signals from different analysis methods")
                combined_confidence *= 0.8
            
            return {
                'signal': combined_signal,
                'confidence': min(combined_confidence, 1.0),
                'reasoning': reasoning
            }
            
        except Exception as e:
            logger.error(f"Error generating final recommendation: {str(e)}")
            return {'signal': 'NEUTRAL', 'confidence': 0.5, 'reasoning': ['Error in analysis']}
    
    def _calculate_stop_loss(self, df: pd.DataFrame, signal: str) -> float:
        """Calculate stop loss level"""
        try:
            current_price = df['Close'].iloc[-1]
            atr = df['ATR'].iloc[-1] if 'ATR' in df.columns else current_price * 0.02
            
            if 'BUY' in signal:
                # For buy signals, stop loss below current price
                return current_price - (2 * atr)
            elif 'SELL' in signal:
                # For sell signals, stop loss above current price
                return current_price + (2 * atr)
            else:
                return current_price
                
        except Exception as e:
            logger.error(f"Error calculating stop loss: {str(e)}")
            return df['Close'].iloc[-1]
    
    def _calculate_take_profit(self, df: pd.DataFrame, signal: str) -> float:
        """Calculate take profit level"""
        try:
            current_price = df['Close'].iloc[-1]
            atr = df['ATR'].iloc[-1] if 'ATR' in df.columns else current_price * 0.02
            
            if 'BUY' in signal:
                # For buy signals, take profit above current price
                return current_price + (3 * atr)
            elif 'SELL' in signal:
                # For sell signals, take profit below current price
                return current_price - (3 * atr)
            else:
                return current_price
                
        except Exception as e:
            logger.error(f"Error calculating take profit: {str(e)}")
            return df['Close'].iloc[-1]
    
    def _recommend_position_size(self, risk_assessment: Dict) -> str:
        """Recommend position size based on risk"""
        risk_level = risk_assessment.get('risk_level', 'MEDIUM')
        
        if risk_level == 'LOW':
            return "NORMAL (2-3% of portfolio)"
        elif risk_level == 'MEDIUM':
            return "REDUCED (1-2% of portfolio)"
        elif risk_level == 'HIGH':
            return "SMALL (0.5-1% of portfolio)"
        else:
            return "AVOID or MICRO (< 0.5% of portfolio)"
    
    def generate_batch_signals(self, symbols: List[str], data_dict: Dict[str, pd.DataFrame]) -> Dict[str, Dict]:
        """Generate signals for multiple symbols"""
        results = {}
        
        for symbol in symbols:
            try:
                if symbol in data_dict and not data_dict[symbol].empty:
                    results[symbol] = self.generate_comprehensive_signals(symbol, data_dict[symbol])
                else:
                    logger.warning(f"No data available for {symbol}")
                    results[symbol] = {
                        'symbol': symbol,
                        'primary_signal': 'NO_DATA',
                        'confidence': 0.0,
                        'reasoning': ['No data available']
                    }
            except Exception as e:
                logger.error(f"Error generating signals for {symbol}: {str(e)}")
                results[symbol] = {
                    'symbol': symbol,
                    'primary_signal': 'ERROR',
                    'confidence': 0.0,
                    'reasoning': [f'Error: {str(e)}']
                }
        
        return results
