"""
Live Trading Example - Demonstrates how to use the enhanced AI trading system
"""
import asyncio
import websockets
import json
import requests
import time
from datetime import datetime

# Base URL for the API
BASE_URL = "http://localhost:8000"

class LiveTradingClient:
    def __init__(self, base_url=BASE_URL):
        self.base_url = base_url
        self.ws = None
        
    async def connect_websocket(self):
        """Connect to the live trading WebSocket"""
        try:
            uri = f"ws://localhost:8000/ws/live"
            self.ws = await websockets.connect(uri)
            print("✅ Connected to live trading WebSocket")
            
            # Listen for messages
            async for message in self.ws:
                data = json.loads(message)
                await self.handle_websocket_message(data)
                
        except Exception as e:
            print(f"❌ WebSocket error: {e}")
    
    async def handle_websocket_message(self, data):
        """Handle incoming WebSocket messages"""
        msg_type = data.get('type')
        timestamp = data.get('timestamp', datetime.now().isoformat())
        
        if msg_type == 'connection_established':
            print(f"🔗 Connection established at {timestamp}")
            market_status = data.get('market_status', {})
            print(f"📊 Market Status: {market_status.get('status', 'UNKNOWN')}")
            
        elif msg_type == 'market_update':
            print(f"📈 Market Update at {timestamp}")
            for symbol, info in data.get('data', {}).items():
                price = info.get('price', 'N/A')
                signal = info.get('signal', 'NEUTRAL')
                print(f"   {symbol}: ${price} - {signal}")
                
        elif msg_type == 'signal_change':
            symbol = data.get('symbol')
            signal = data.get('signal')
            confidence = data.get('confidence', 0)
            print(f"🚨 SIGNAL CHANGE: {symbol} → {signal} (Confidence: {confidence:.2%})")
            
        elif msg_type == 'price_alert':
            print(f"🔔 PRICE ALERT: {data.get('message')}")
    
    def start_monitoring(self, symbols):
        """Start live monitoring for symbols"""
        try:
            response = requests.post(f"{self.base_url}/live/monitor", json={
                "symbols": symbols,
                "enable_alerts": True
            })
            
            if response.status_code == 200:
                print(f"✅ Started monitoring {len(symbols)} symbols")
                return response.json()
            else:
                print(f"❌ Failed to start monitoring: {response.text}")
                return None
                
        except Exception as e:
            print(f"❌ Error starting monitoring: {e}")
            return None
    
    def scan_market(self, scan_type="breakout", limit=10):
        """Perform market scan"""
        try:
            response = requests.post(f"{self.base_url}/scan", json={
                "scan_type": scan_type,
                "limit": limit
            })
            
            if response.status_code == 200:
                data = response.json()
                opportunities = data.get('opportunities', [])
                print(f"🎯 Found {len(opportunities)} {scan_type} opportunities:")
                
                for opp in opportunities[:5]:  # Show top 5
                    symbol = opp.get('symbol')
                    score = opp.get('score', 0)
                    price = opp.get('current_price', 0)
                    reasons = opp.get('reasons', [])
                    
                    print(f"   {symbol}: ${price:.2f} (Score: {score})")
                    print(f"      Reasons: {', '.join(reasons[:2])}")
                
                return opportunities
            else:
                print(f"❌ Scan failed: {response.text}")
                return []
                
        except Exception as e:
            print(f"❌ Error scanning market: {e}")
            return []
    
    def get_trading_signals(self, symbol):
        """Get comprehensive trading signals for a symbol"""
        try:
            response = requests.get(f"{self.base_url}/signals/{symbol}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"📊 Trading Signals for {symbol}:")
                print(f"   Signal: {data.get('primary_signal')}")
                print(f"   Confidence: {data.get('confidence', 0):.2%}")
                print(f"   Risk Level: {data.get('risk_level')}")
                print(f"   Entry Price: ${data.get('entry_price', 0):.2f}")
                print(f"   Stop Loss: ${data.get('stop_loss', 0):.2f}")
                print(f"   Take Profit: ${data.get('take_profit', 0):.2f}")
                
                reasoning = data.get('reasoning', [])
                if reasoning:
                    print(f"   Reasoning: {', '.join(reasoning)}")
                
                return data
            else:
                print(f"❌ Failed to get signals for {symbol}: {response.text}")
                return None
                
        except Exception as e:
            print(f"❌ Error getting signals: {e}")
            return None
    
    def add_price_alert(self, symbol, condition, price, message=""):
        """Add a price alert"""
        try:
            response = requests.post(f"{self.base_url}/live/alert", json={
                "symbol": symbol,
                "condition": condition,  # 'above', 'below', 'change'
                "price": price,
                "message": message
            })
            
            if response.status_code == 200:
                print(f"✅ Added price alert for {symbol}: {condition} ${price}")
                return True
            else:
                print(f"❌ Failed to add alert: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Error adding alert: {e}")
            return False
    
    def get_portfolio_summary(self):
        """Get portfolio summary"""
        try:
            response = requests.get(f"{self.base_url}/portfolio/summary")

            if response.status_code == 200:
                data = response.json()
                metrics = data.get('performance_metrics', {})

                print("💼 Portfolio Summary:")
                print(f"   Portfolio Value: ${metrics.get('portfolio_value', 0):,.2f}")
                print(f"   Total Return: {metrics.get('total_return_pct', 0):+.2f}%")
                print(f"   Realized P&L: ${metrics.get('realized_pnl', 0):+,.2f}")
                print(f"   Unrealized P&L: ${metrics.get('unrealized_pnl', 0):+,.2f}")
                print(f"   Win Rate: {metrics.get('win_rate_pct', 0):.1f}%")
                print(f"   Open Positions: {metrics.get('open_positions', 0)}")

                return data
            else:
                print(f"❌ Failed to get portfolio summary: {response.text}")
                return None

        except Exception as e:
            print(f"❌ Error getting portfolio: {e}")
            return None

    def get_ai_analysis(self, symbol, timeframe="1d"):
        """Get AI-powered analysis with buy/sell suggestions"""
        try:
            response = requests.post(f"{self.base_url}/ai/analyze", json={
                "symbol": symbol,
                "timeframe": timeframe,
                "include_notifications": True
            })

            if response.status_code == 200:
                data = response.json()
                analysis = data.get('analysis', {})

                print(f"🤖 AI Analysis for {symbol}:")

                # Suggestions
                suggestions = analysis.get('suggestions', {})
                print(f"   Action: {suggestions.get('action', 'HOLD')}")
                print(f"   Confidence: {suggestions.get('confidence', 0):.1%}")
                print(f"   Target Price: ${suggestions.get('target_price', 0):.2f}")

                # Profit Potential
                profit_potential = analysis.get('profit_potential', {})
                scenarios = profit_potential.get('scenarios', [])
                if scenarios:
                    best_scenario = scenarios[-1]  # Largest investment
                    print(f"   Profit Potential (${best_scenario.get('investment', 0)} investment):")
                    print(f"      Potential Profit: ${best_scenario.get('potential_profit', 0):.2f}")
                    print(f"      Profit %: {best_scenario.get('profit_percentage', 0):+.1f}%")

                # Risk and Sentiment
                print(f"   Risk Level: {analysis.get('risk_level', 'Unknown')}")
                print(f"   Market Sentiment: {analysis.get('market_sentiment', 'Unknown')}")

                # Reasoning
                reasoning = suggestions.get('reasoning', [])
                if reasoning:
                    print(f"   Reasoning: {', '.join(reasoning)}")

                return analysis
            else:
                print(f"❌ Failed to get AI analysis for {symbol}: {response.text}")
                return None

        except Exception as e:
            print(f"❌ Error getting AI analysis: {e}")
            return None

    def search_companies(self, query, limit=10):
        """Search for companies"""
        try:
            response = requests.post(f"{self.base_url}/search/companies", json={
                "query": query,
                "limit": limit
            })

            if response.status_code == 200:
                data = response.json()
                companies = data.get('companies', [])

                print(f"🔍 Search Results for '{query}':")
                for company in companies:
                    symbol = company.get('symbol', 'N/A')
                    name = company.get('name', 'N/A')
                    price = company.get('current_price', 0)
                    sector = company.get('sector', 'N/A')

                    print(f"   {symbol}: {name}")
                    print(f"      Price: ${price:.2f} | Sector: {sector}")

                return companies
            else:
                print(f"❌ Company search failed: {response.text}")
                return []

        except Exception as e:
            print(f"❌ Error searching companies: {e}")
            return []

    def get_company_details(self, symbol):
        """Get comprehensive company details"""
        try:
            response = requests.get(f"{self.base_url}/company/{symbol}")

            if response.status_code == 200:
                data = response.json()
                company_data = data.get('company_data', {})

                print(f"🏢 Company Details for {symbol}:")
                print(f"   Name: {company_data.get('name', 'N/A')}")
                print(f"   Sector: {company_data.get('sector', 'N/A')}")
                print(f"   Industry: {company_data.get('industry', 'N/A')}")
                print(f"   Exchange: {company_data.get('exchange', 'N/A')}")
                print(f"   Country: {company_data.get('country', 'N/A')}")

                # Financial metrics
                financial = company_data.get('financial_metrics', {})
                market_cap = financial.get('market_cap', 0)
                revenue = financial.get('revenue', 0)

                print(f"   Market Cap: ${market_cap:,.0f}")
                print(f"   Revenue: ${revenue:,.0f}")

                # Stock performance
                performance = company_data.get('stock_performance', {})
                current_price = performance.get('current_price', 0)
                ytd_return = performance.get('year_to_date_return', 0)

                print(f"   Current Price: ${current_price:.2f}")
                print(f"   YTD Return: {ytd_return:+.1f}%")

                return company_data
            else:
                print(f"❌ Failed to get company details: {response.text}")
                return None

        except Exception as e:
            print(f"❌ Error getting company details: {e}")
            return None

    def get_notifications(self, limit=10):
        """Get recent notifications"""
        try:
            response = requests.get(f"{self.base_url}/notifications", params={
                "limit": limit,
                "unread_only": False
            })

            if response.status_code == 200:
                data = response.json()
                notifications = data.get('notifications', [])

                print(f"🔔 Recent Notifications ({len(notifications)}):")
                for notif in notifications:
                    title = notif.get('title', 'No title')
                    message = notif.get('message', 'No message')
                    priority = notif.get('priority', 'LOW')

                    print(f"   [{priority}] {title}")
                    print(f"      {message}")

                return notifications
            else:
                print(f"❌ Failed to get notifications: {response.text}")
                return []

        except Exception as e:
            print(f"❌ Error getting notifications: {e}")
            return []

async def main():
    """Main example function demonstrating all AI trading features"""
    print("🚀 Enhanced AI Trading System - Complete Demo")
    print("=" * 60)

    # Initialize client
    client = LiveTradingClient()

    # Example 1: AI-Powered Analysis with Profit Predictions
    print("\n🤖 AI-Powered Analysis with Buy/Sell Suggestions...")
    ai_symbols = ['AAPL', 'GOOGL', 'TSLA']

    for symbol in ai_symbols:
        print(f"\n--- AI ANALYSIS: {symbol} ---")
        analysis = client.get_ai_analysis(symbol)
        if analysis:
            time.sleep(2)  # Rate limiting

    # Example 2: Company Search and Details
    print("\n🔍 Company Search Demonstration...")
    search_queries = ['Apple', 'Tesla', 'Microsoft']

    for query in search_queries:
        print(f"\n--- SEARCHING: {query} ---")
        companies = client.search_companies(query, limit=3)

        # Get detailed info for first result
        if companies:
            first_company = companies[0]
            symbol = first_company.get('symbol')
            if symbol:
                print(f"\n--- COMPANY DETAILS: {symbol} ---")
                client.get_company_details(symbol)

        time.sleep(1)

    # Example 3: Get trading signals for popular stocks
    print("\n📊 Traditional Trading Signals...")
    symbols = ['AAPL', 'GOOGL', 'TSLA', 'MSFT']

    for symbol in symbols:
        signals = client.get_trading_signals(symbol)
        if signals:
            time.sleep(1)  # Rate limiting

    # Example 4: Perform market scans
    print("\n🎯 Market Opportunity Scanning...")
    scan_types = ['breakout', 'oversold', 'momentum']

    for scan_type in scan_types:
        print(f"\n--- {scan_type.upper()} SCAN ---")
        opportunities = client.scan_market(scan_type, limit=5)
        time.sleep(2)  # Rate limiting

    # Example 5: Notifications
    print("\n🔔 Recent Notifications...")
    notifications = client.get_notifications(limit=5)

    # Example 6: Start live monitoring
    print("\n🔴 Starting Live Monitoring...")
    monitoring_symbols = ['AAPL', 'GOOGL', 'TSLA', 'MSFT', 'AMZN']
    client.start_monitoring(monitoring_symbols)

    # Example 7: Add price alerts
    print("\n🔔 Adding Price Alerts...")
    client.add_price_alert('AAPL', 'above', 200.0, "AAPL broke $200!")
    client.add_price_alert('TSLA', 'below', 150.0, "TSLA dropped below $150")

    # Example 8: Get portfolio summary
    print("\n💼 Portfolio Summary...")
    client.get_portfolio_summary()

    # Example 9: Connect to WebSocket for live updates
    print("\n🔗 Connecting to Enhanced WebSocket...")
    print("(This will run indefinitely - press Ctrl+C to stop)")
    print("Features: Live prices, AI alerts, profit notifications, company search")

    try:
        await client.connect_websocket()
    except KeyboardInterrupt:
        print("\n👋 Disconnected from AI trading system")

def run_basic_example():
    """Run basic example without WebSocket"""
    print("🚀 Enhanced AI Trading System - Basic Demo")
    print("=" * 50)

    client = LiveTradingClient()

    # Test API connectivity
    try:
        response = requests.get(f"{client.base_url}/health")
        if response.status_code == 200:
            print("✅ API is running")
        else:
            print("❌ API not responding")
            return
    except Exception as e:
        print(f"❌ Cannot connect to API: {e}")
        print("Make sure the server is running with: python run_server.py")
        return

    # Demo 1: AI Analysis with Profit Predictions
    print("\n🤖 AI Analysis Demo...")
    test_symbols = ['AAPL', 'GOOGL', 'TSLA']

    for symbol in test_symbols:
        print(f"\n--- AI ANALYSIS: {symbol} ---")
        analysis = client.get_ai_analysis(symbol)
        time.sleep(1)

    # Demo 2: Company Search
    print("\n🔍 Company Search Demo...")
    search_results = client.search_companies('Apple', limit=3)
    if search_results:
        first_symbol = search_results[0].get('symbol')
        if first_symbol:
            print(f"\n--- COMPANY DETAILS: {first_symbol} ---")
            client.get_company_details(first_symbol)

    # Demo 3: Traditional Trading Signals
    print("\n📊 Trading Signals Demo...")
    for symbol in test_symbols:
        print(f"\n--- SIGNALS: {symbol} ---")
        signals = client.get_trading_signals(symbol)
        time.sleep(1)

    # Demo 4: Market Scan
    print("\n🎯 Market Scan Demo...")
    opportunities = client.scan_market('breakout', limit=5)

    # Demo 5: Notifications
    print("\n🔔 Notifications Demo...")
    notifications = client.get_notifications(limit=3)

    print("\n✅ Enhanced basic demo completed!")
    print("💡 Features demonstrated:")
    print("   🤖 AI-powered buy/sell suggestions with profit predictions")
    print("   🔍 Company search with live trade data")
    print("   📊 Traditional technical analysis")
    print("   🎯 Market opportunity scanning")
    print("   🔔 Profit alert notifications")
    print("\n💡 For live WebSocket features, run: python examples/live_trading_example.py")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "--basic":
        # Run basic example without WebSocket
        run_basic_example()
    else:
        # Run full live example with WebSocket
        try:
            asyncio.run(main())
        except KeyboardInterrupt:
            print("\n👋 Example stopped by user")
        except Exception as e:
            print(f"\n❌ Error: {e}")
            print("\n💡 Try running with --basic flag for a simpler example")
            print("   python examples/live_trading_example.py --basic")
