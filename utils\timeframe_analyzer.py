"""
Multi-timeframe Analysis for correlating yesterday's, today's, and tomorrow's trends
"""
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
from loguru import logger
from utils.technical_analysis import TechnicalAnalyzer
from models.ai_predictor import AIPredictor
from config import Config

class TimeframeAnalyzer:
    def __init__(self):
        self.config = Config()
        self.technical_analyzer = TechnicalAnalyzer()
        self.ai_predictor = AIPredictor()
        
    def analyze_temporal_trends(self, df: pd.DataFrame, symbol: str) -> Dict:
        """
        Analyze trends across multiple timeframes (yesterday, today, tomorrow)
        
        Args:
            df: DataFrame with historical price data
            symbol: Stock symbol
            
        Returns:
            Dictionary with temporal trend analysis
        """
        try:
            # Ensure we have enough data
            if len(df) < 50:
                logger.warning(f"Insufficient data for temporal analysis of {symbol}")
                return {}
            
            # Calculate technical indicators
            df_with_indicators = self.technical_analyzer.calculate_indicators(df)
            df_with_patterns = self.technical_analyzer.detect_candlestick_patterns(df_with_indicators)
            
            # Get yesterday's analysis
            yesterday_analysis = self._analyze_yesterday(df_with_patterns)
            
            # Get today's analysis
            today_analysis = self._analyze_today(df_with_patterns)
            
            # Predict tomorrow's trends
            tomorrow_prediction = self._predict_tomorrow(df_with_patterns, symbol)
            
            # Calculate temporal correlations
            temporal_correlation = self._calculate_temporal_correlation(df_with_patterns)
            
            # Generate overall assessment
            overall_assessment = self._generate_overall_assessment(
                yesterday_analysis, today_analysis, tomorrow_prediction, temporal_correlation
            )
            
            return {
                'symbol': symbol,
                'analysis_timestamp': datetime.now(),
                'yesterday': yesterday_analysis,
                'today': today_analysis,
                'tomorrow': tomorrow_prediction,
                'temporal_correlation': temporal_correlation,
                'overall_assessment': overall_assessment
            }
            
        except Exception as e:
            logger.error(f"Error in temporal trend analysis for {symbol}: {str(e)}")
            return {}
    
    def _analyze_yesterday(self, df: pd.DataFrame) -> Dict:
        """Analyze yesterday's trading data"""
        try:
            if len(df) < 2:
                return {}
            
            yesterday_data = df.iloc[-2]  # Second to last row
            day_before_data = df.iloc[-3] if len(df) >= 3 else df.iloc[-2]
            
            # Price movement
            price_change = (yesterday_data['Close'] - yesterday_data['Open']) / yesterday_data['Open'] * 100
            price_change_from_prev = (yesterday_data['Close'] - day_before_data['Close']) / day_before_data['Close'] * 100
            
            # Volume analysis
            volume_change = (yesterday_data['Volume'] - day_before_data['Volume']) / day_before_data['Volume'] * 100 if day_before_data['Volume'] > 0 else 0
            
            # Technical indicators
            rsi = yesterday_data.get('RSI', 50)
            macd_signal = 'BUY' if yesterday_data.get('MACD', 0) > yesterday_data.get('MACD_Signal', 0) else 'SELL'
            
            # Trend determination
            trend = 'BULLISH' if price_change > 0 else 'BEARISH'
            trend_strength = abs(price_change)
            
            return {
                'date': yesterday_data.name if hasattr(yesterday_data, 'name') else 'Yesterday',
                'open': yesterday_data['Open'],
                'high': yesterday_data['High'],
                'low': yesterday_data['Low'],
                'close': yesterday_data['Close'],
                'volume': yesterday_data['Volume'],
                'price_change_pct': price_change,
                'price_change_from_prev_pct': price_change_from_prev,
                'volume_change_pct': volume_change,
                'trend': trend,
                'trend_strength': trend_strength,
                'rsi': rsi,
                'macd_signal': macd_signal,
                'volatility': ((yesterday_data['High'] - yesterday_data['Low']) / yesterday_data['Close']) * 100
            }
            
        except Exception as e:
            logger.error(f"Error analyzing yesterday's data: {str(e)}")
            return {}
    
    def _analyze_today(self, df: pd.DataFrame) -> Dict:
        """Analyze today's trading data and current market conditions"""
        try:
            if df.empty:
                return {}
            
            today_data = df.iloc[-1]  # Latest row
            yesterday_data = df.iloc[-2] if len(df) >= 2 else today_data
            
            # Current price movement
            price_change = (today_data['Close'] - today_data['Open']) / today_data['Open'] * 100
            price_change_from_yesterday = (today_data['Close'] - yesterday_data['Close']) / yesterday_data['Close'] * 100
            
            # Volume analysis
            volume_change = (today_data['Volume'] - yesterday_data['Volume']) / yesterday_data['Volume'] * 100 if yesterday_data['Volume'] > 0 else 0
            
            # Technical analysis
            trend_analysis = self.technical_analyzer.analyze_trend(df)
            pattern_summary = self.technical_analyzer.get_pattern_summary(df)
            support_resistance = self.technical_analyzer.detect_support_resistance(df)
            volatility = self.technical_analyzer.calculate_volatility(df)
            
            # Intraday momentum
            intraday_momentum = self._calculate_intraday_momentum(today_data)
            
            return {
                'date': today_data.name if hasattr(today_data, 'name') else 'Today',
                'current_price': today_data['Close'],
                'open': today_data['Open'],
                'high': today_data['High'],
                'low': today_data['Low'],
                'volume': today_data['Volume'],
                'price_change_pct': price_change,
                'price_change_from_yesterday_pct': price_change_from_yesterday,
                'volume_change_pct': volume_change,
                'trend_analysis': trend_analysis,
                'pattern_summary': pattern_summary,
                'support_resistance': support_resistance,
                'volatility': volatility,
                'intraday_momentum': intraday_momentum
            }
            
        except Exception as e:
            logger.error(f"Error analyzing today's data: {str(e)}")
            return {}
    
    def _predict_tomorrow(self, df: pd.DataFrame, symbol: str) -> Dict:
        """Predict tomorrow's trends using AI models"""
        try:
            # Train models if not already trained
            if not hasattr(self.ai_predictor, 'models') or not self.ai_predictor.models:
                logger.info(f"Training AI models for {symbol}")
                self.ai_predictor.train_regression_models(df)
            
            # Get price predictions
            price_predictions = self.ai_predictor.predict_price(df, days_ahead=1)
            
            # Get trend direction prediction
            trend_prediction = self.ai_predictor.predict_trend_direction(df)
            
            # Calculate expected price range
            current_price = df['Close'].iloc[-1]
            volatility = self.technical_analyzer.calculate_volatility(df)
            
            # Estimate price range based on volatility
            daily_volatility = volatility.get('historical_volatility', 0.02) / np.sqrt(252)
            price_range_low = current_price * (1 - daily_volatility * 2)
            price_range_high = current_price * (1 + daily_volatility * 2)
            
            # Generate trading signals
            signals = self._generate_tomorrow_signals(price_predictions, trend_prediction, current_price)
            
            return {
                'predicted_price': price_predictions.get('Ensemble', current_price),
                'price_predictions': price_predictions,
                'trend_prediction': trend_prediction,
                'expected_price_range': {
                    'low': price_range_low,
                    'high': price_range_high
                },
                'confidence_level': trend_prediction.get('confidence', 0),
                'trading_signals': signals,
                'risk_assessment': self._assess_tomorrow_risk(df, price_predictions, trend_prediction)
            }
            
        except Exception as e:
            logger.error(f"Error predicting tomorrow's trends: {str(e)}")
            return {}
    
    def _calculate_temporal_correlation(self, df: pd.DataFrame) -> Dict:
        """Calculate correlations between different timeframes"""
        try:
            if len(df) < 10:
                return {}
            
            # Calculate daily returns
            df['Daily_Returns'] = df['Close'].pct_change()
            
            # Calculate correlations with different lags
            correlations = {}
            for lag in [1, 2, 3, 5]:
                if len(df) > lag:
                    correlation = df['Daily_Returns'].corr(df['Daily_Returns'].shift(lag))
                    correlations[f'lag_{lag}_days'] = correlation if not np.isnan(correlation) else 0
            
            # Calculate momentum persistence
            positive_days = (df['Daily_Returns'] > 0).rolling(window=5).sum()
            momentum_persistence = positive_days.iloc[-1] / 5 if len(positive_days) > 0 else 0.5
            
            # Calculate trend consistency
            trend_consistency = self._calculate_trend_consistency(df)
            
            return {
                'price_correlations': correlations,
                'momentum_persistence': momentum_persistence,
                'trend_consistency': trend_consistency,
                'volatility_clustering': self._detect_volatility_clustering(df)
            }
            
        except Exception as e:
            logger.error(f"Error calculating temporal correlation: {str(e)}")
            return {}
    
    def _calculate_intraday_momentum(self, today_data) -> Dict:
        """Calculate intraday momentum indicators"""
        try:
            open_price = today_data['Open']
            high_price = today_data['High']
            low_price = today_data['Low']
            close_price = today_data['Close']
            
            # Body size (open to close)
            body_size = abs(close_price - open_price) / open_price * 100
            
            # Upper shadow (high to max of open/close)
            upper_shadow = (high_price - max(open_price, close_price)) / open_price * 100
            
            # Lower shadow (min of open/close to low)
            lower_shadow = (min(open_price, close_price) - low_price) / open_price * 100
            
            # Momentum direction
            momentum_direction = 'BULLISH' if close_price > open_price else 'BEARISH'
            
            return {
                'body_size_pct': body_size,
                'upper_shadow_pct': upper_shadow,
                'lower_shadow_pct': lower_shadow,
                'momentum_direction': momentum_direction,
                'intraday_range_pct': ((high_price - low_price) / open_price) * 100
            }
            
        except Exception as e:
            logger.error(f"Error calculating intraday momentum: {str(e)}")
            return {}
    
    def _generate_tomorrow_signals(self, price_predictions: Dict, trend_prediction: Dict, current_price: float) -> Dict:
        """Generate trading signals for tomorrow"""
        try:
            signals = {
                'primary_signal': 'HOLD',
                'confidence': 0,
                'reasoning': []
            }
            
            # Price-based signals
            if price_predictions:
                predicted_price = price_predictions.get('Ensemble', current_price)
                price_change_pct = (predicted_price - current_price) / current_price * 100
                
                if price_change_pct > 2:
                    signals['primary_signal'] = 'STRONG_BUY'
                    signals['reasoning'].append(f"Predicted price increase of {price_change_pct:.2f}%")
                elif price_change_pct > 0.5:
                    signals['primary_signal'] = 'BUY'
                    signals['reasoning'].append(f"Predicted price increase of {price_change_pct:.2f}%")
                elif price_change_pct < -2:
                    signals['primary_signal'] = 'STRONG_SELL'
                    signals['reasoning'].append(f"Predicted price decrease of {abs(price_change_pct):.2f}%")
                elif price_change_pct < -0.5:
                    signals['primary_signal'] = 'SELL'
                    signals['reasoning'].append(f"Predicted price decrease of {abs(price_change_pct):.2f}%")
            
            # Trend-based signals
            if trend_prediction:
                trend_conf = trend_prediction.get('confidence', 0)
                trend_dir = trend_prediction.get('prediction', 'UNKNOWN')
                
                if trend_conf > 0.7:
                    if trend_dir == 'UP':
                        signals['reasoning'].append(f"Strong upward trend prediction (confidence: {trend_conf:.2f})")
                    else:
                        signals['reasoning'].append(f"Strong downward trend prediction (confidence: {trend_conf:.2f})")
                
                signals['confidence'] = trend_conf
            
            return signals
            
        except Exception as e:
            logger.error(f"Error generating tomorrow's signals: {str(e)}")
            return {'primary_signal': 'HOLD', 'confidence': 0, 'reasoning': []}
    
    def _assess_tomorrow_risk(self, df: pd.DataFrame, price_predictions: Dict, trend_prediction: Dict) -> Dict:
        """Assess risk for tomorrow's trading"""
        try:
            risk_factors = []
            risk_score = 0.5  # Neutral risk
            
            # Volatility risk
            volatility = self.technical_analyzer.calculate_volatility(df)
            hist_vol = volatility.get('historical_volatility', 0)
            
            if hist_vol > 0.3:  # High volatility
                risk_factors.append("High historical volatility")
                risk_score += 0.2
            elif hist_vol < 0.1:  # Low volatility
                risk_factors.append("Low volatility - potential for sudden moves")
                risk_score += 0.1
            
            # Prediction confidence risk
            confidence = trend_prediction.get('confidence', 0)
            if confidence < 0.6:
                risk_factors.append("Low prediction confidence")
                risk_score += 0.2
            
            # Model agreement risk
            if len(price_predictions) > 1:
                predictions = list(price_predictions.values())
                pred_std = np.std(predictions) / np.mean(predictions)
                if pred_std > 0.05:  # High disagreement between models
                    risk_factors.append("High disagreement between prediction models")
                    risk_score += 0.15
            
            # Determine risk level
            if risk_score > 0.8:
                risk_level = "VERY_HIGH"
            elif risk_score > 0.6:
                risk_level = "HIGH"
            elif risk_score > 0.4:
                risk_level = "MEDIUM"
            else:
                risk_level = "LOW"
            
            return {
                'risk_level': risk_level,
                'risk_score': min(risk_score, 1.0),
                'risk_factors': risk_factors
            }
            
        except Exception as e:
            logger.error(f"Error assessing tomorrow's risk: {str(e)}")
            return {'risk_level': 'UNKNOWN', 'risk_score': 0.5, 'risk_factors': []}
    
    def _calculate_trend_consistency(self, df: pd.DataFrame) -> float:
        """Calculate how consistent the trend has been"""
        try:
            if len(df) < 10:
                return 0.5
            
            # Calculate moving averages
            df['SMA_5'] = df['Close'].rolling(window=5).mean()
            df['SMA_10'] = df['Close'].rolling(window=10).mean()
            
            # Check trend consistency over last 10 days
            recent_data = df.tail(10)
            
            # Count days where short MA > long MA (uptrend)
            uptrend_days = (recent_data['SMA_5'] > recent_data['SMA_10']).sum()
            
            # Consistency score (0 = always downtrend, 1 = always uptrend, 0.5 = mixed)
            consistency = uptrend_days / len(recent_data)
            
            return consistency
            
        except Exception as e:
            logger.error(f"Error calculating trend consistency: {str(e)}")
            return 0.5
    
    def _detect_volatility_clustering(self, df: pd.DataFrame) -> Dict:
        """Detect volatility clustering patterns"""
        try:
            if len(df) < 20:
                return {}
            
            # Calculate daily volatility
            df['Daily_Vol'] = ((df['High'] - df['Low']) / df['Close']).rolling(window=1).mean()
            
            # Calculate rolling volatility
            df['Vol_MA'] = df['Daily_Vol'].rolling(window=10).mean()
            
            # Detect clustering
            recent_vol = df['Daily_Vol'].tail(5).mean()
            avg_vol = df['Vol_MA'].tail(1).iloc[0]
            
            clustering_ratio = recent_vol / avg_vol if avg_vol > 0 else 1
            
            return {
                'clustering_detected': clustering_ratio > 1.5,
                'clustering_ratio': clustering_ratio,
                'recent_volatility': recent_vol,
                'average_volatility': avg_vol
            }
            
        except Exception as e:
            logger.error(f"Error detecting volatility clustering: {str(e)}")
            return {}
    
    def _generate_overall_assessment(self, yesterday: Dict, today: Dict, tomorrow: Dict, correlation: Dict) -> Dict:
        """Generate overall assessment combining all timeframes"""
        try:
            # Combine trend signals from all timeframes
            trend_signals = []
            
            if yesterday:
                trend_signals.append(yesterday.get('trend', 'NEUTRAL'))
            
            if today and 'trend_analysis' in today:
                trend_signals.append(today['trend_analysis'].get('overall_trend', 'NEUTRAL'))
            
            if tomorrow and 'trend_prediction' in tomorrow:
                pred = tomorrow['trend_prediction'].get('prediction', 'UNKNOWN')
                trend_signals.append('BULLISH' if pred == 'UP' else 'BEARISH' if pred == 'DOWN' else 'NEUTRAL')
            
            # Calculate overall trend
            bullish_count = trend_signals.count('BULLISH')
            bearish_count = trend_signals.count('BEARISH')
            
            if bullish_count > bearish_count:
                overall_trend = 'BULLISH'
            elif bearish_count > bullish_count:
                overall_trend = 'BEARISH'
            else:
                overall_trend = 'NEUTRAL'
            
            # Calculate confidence based on consistency
            trend_consistency = len([t for t in trend_signals if t == overall_trend]) / len(trend_signals) if trend_signals else 0
            
            return {
                'overall_trend': overall_trend,
                'trend_consistency': trend_consistency,
                'timeframe_alignment': trend_consistency > 0.6,
                'recommendation': self._get_final_recommendation(overall_trend, trend_consistency, tomorrow),
                'key_insights': self._generate_key_insights(yesterday, today, tomorrow, correlation)
            }
            
        except Exception as e:
            logger.error(f"Error generating overall assessment: {str(e)}")
            return {'overall_trend': 'UNKNOWN', 'trend_consistency': 0}
    
    def _get_final_recommendation(self, trend: str, consistency: float, tomorrow: Dict) -> str:
        """Get final trading recommendation"""
        if consistency < 0.5:
            return "WAIT - Mixed signals across timeframes"
        
        if trend == 'BULLISH' and consistency > 0.7:
            return "BUY - Strong bullish alignment"
        elif trend == 'BEARISH' and consistency > 0.7:
            return "SELL - Strong bearish alignment"
        elif trend == 'BULLISH':
            return "CAUTIOUS BUY - Moderate bullish signals"
        elif trend == 'BEARISH':
            return "CAUTIOUS SELL - Moderate bearish signals"
        else:
            return "HOLD - Neutral trend"
    
    def _generate_key_insights(self, yesterday: Dict, today: Dict, tomorrow: Dict, correlation: Dict) -> List[str]:
        """Generate key insights from the analysis"""
        insights = []
        
        try:
            # Yesterday to today momentum
            if yesterday and today:
                yesterday_trend = yesterday.get('trend', 'NEUTRAL')
                today_change = today.get('price_change_from_yesterday_pct', 0)
                
                if yesterday_trend == 'BULLISH' and today_change > 0:
                    insights.append("Positive momentum continuing from yesterday")
                elif yesterday_trend == 'BEARISH' and today_change < 0:
                    insights.append("Negative momentum continuing from yesterday")
                elif yesterday_trend != 'NEUTRAL' and abs(today_change) > 2:
                    insights.append("Trend reversal detected from yesterday to today")
            
            # Volume insights
            if today and 'volume_change_pct' in today:
                vol_change = today['volume_change_pct']
                if vol_change > 50:
                    insights.append("Unusually high volume - increased interest")
                elif vol_change < -30:
                    insights.append("Low volume - lack of conviction")
            
            # Volatility insights
            if today and 'volatility' in today:
                vol = today['volatility'].get('historical_volatility', 0)
                if vol > 0.3:
                    insights.append("High volatility - expect significant price swings")
                elif vol < 0.1:
                    insights.append("Low volatility - potential for breakout")
            
            # Prediction confidence
            if tomorrow and 'confidence_level' in tomorrow:
                conf = tomorrow['confidence_level']
                if conf > 0.8:
                    insights.append("High confidence in tomorrow's prediction")
                elif conf < 0.5:
                    insights.append("Low confidence - proceed with caution")
            
            return insights[:5]  # Limit to top 5 insights
            
        except Exception as e:
            logger.error(f"Error generating insights: {str(e)}")
            return ["Analysis completed with limited insights due to data constraints"]
