"""
AI-Powered Trading Analysis Backend
Main FastAPI application for comprehensive stock analysis and trading signals
"""
from fastapi import FastAPI, HTTPException, BackgroundTasks, Query
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse
from pydantic import BaseModel
from typing import List, Dict, Optional
import asyncio
import pandas as pd
from datetime import datetime, timedelta
from loguru import logger
import uvicorn

# Import our custom modules
from config import Config
from utils.data_collector import DataCollector
from utils.company_validator import CompanyValidator
from utils.technical_analysis import TechnicalAnalyzer
from utils.timeframe_analyzer import TimeframeAnalyzer
from utils.signal_generator import SignalGenerator
from utils.live_trading_engine import LiveTradingEngine
from utils.market_scanner import MarketScanner
from utils.portfolio_tracker import PortfolioTracker
from utils.ai_trend_analyzer import AITrendAnalyzer
from utils.notification_system import NotificationSystem
from utils.company_search import CompanySearchEngine
from models.ai_predictor import AIPredictor

# Initialize FastAPI app
app = FastAPI(
    title="AI Trading Analysis System",
    description="Comprehensive AI-powered trading analysis with company validation, technical analysis, and predictive modeling",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Mount static files
app.mount("/static", StaticFiles(directory="static"), name="static")

# Initialize components
config = Config()
data_collector = DataCollector()
company_validator = CompanyValidator()
technical_analyzer = TechnicalAnalyzer()
timeframe_analyzer = TimeframeAnalyzer()
signal_generator = SignalGenerator()
ai_predictor = AIPredictor()
live_trading_engine = LiveTradingEngine()
market_scanner = MarketScanner()
portfolio_tracker = PortfolioTracker()
ai_trend_analyzer = AITrendAnalyzer()
notification_system = NotificationSystem()
company_search_engine = CompanySearchEngine()

# Configure logging
logger.add(config.LOG_FILE, rotation="1 day", retention="30 days", level=config.LOG_LEVEL)

# Pydantic models for API requests/responses
class AnalysisRequest(BaseModel):
    symbol: str
    period: str = "1y"
    interval: str = "1d"

class BatchAnalysisRequest(BaseModel):
    symbols: List[str]
    period: str = "1y"
    interval: str = "1d"

class PredictionRequest(BaseModel):
    symbol: str
    days_ahead: int = 1
    period: str = "2y"

class CompanyValidationResponse(BaseModel):
    symbol: str
    is_legitimate: bool
    legitimacy_score: float
    risk_level: str
    red_flags: List[str]

class TradingSignalResponse(BaseModel):
    symbol: str
    primary_signal: str
    confidence: float
    risk_level: str
    entry_price: float
    stop_loss: float
    take_profit: float
    reasoning: List[str]

class LiveMonitoringRequest(BaseModel):
    symbols: List[str]
    enable_alerts: bool = True

class PriceAlertRequest(BaseModel):
    symbol: str
    condition: str  # 'above', 'below', 'change'
    price: float
    message: str = ""

class PositionRequest(BaseModel):
    symbol: str
    position_type: str  # 'LONG' or 'SHORT'
    quantity: int
    entry_price: float
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None
    notes: str = ""

class ScanRequest(BaseModel):
    scan_type: str  # 'breakout', 'oversold', 'momentum', 'gap', 'volume'
    limit: int = 20

class AIAnalysisRequest(BaseModel):
    symbol: str
    timeframe: str = "1d"  # '1d', '1h', '5m'
    include_notifications: bool = True

class CompanySearchRequest(BaseModel):
    query: str
    limit: int = 20

@app.get("/")
async def root():
    """Root endpoint with API information"""
    return {
        "message": "AI Trading Analysis System",
        "version": "1.0.0",
        "status": "active",
        "endpoints": {
            "dashboard": "/dashboard",
            "preview": "/preview",
            "ai_analysis": "/ai/analyze",
            "company_search": "/search/companies",
            "company_details": "/company/{symbol}",
            "live_data": "/company/{symbol}/live",
            "notifications": "/notifications",
            "company_validation": "/validate/{symbol}",
            "technical_analysis": "/technical/{symbol}",
            "trading_signals": "/signals/{symbol}",
            "price_prediction": "/predict/{symbol}",
            "temporal_analysis": "/temporal/{symbol}",
            "batch_analysis": "/batch/analyze",
            "market_scanner": "/scan",
            "live_monitoring": "/live/monitor",
            "portfolio": "/portfolio",
            "health_check": "/health"
        }
    }

@app.get("/dashboard", response_class=HTMLResponse)
async def dashboard():
    """Serve the trading dashboard"""
    with open("static/dashboard.html", "r") as f:
        return HTMLResponse(content=f.read(), status_code=200)

@app.get("/preview", response_class=HTMLResponse)
async def preview():
    """Serve the dashboard preview/mockup"""
    with open("static/preview.html", "r") as f:
        return HTMLResponse(content=f.read(), status_code=200)

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": datetime.now(),
        "components": {
            "data_collector": "active",
            "ai_predictor": "active",
            "signal_generator": "active"
        }
    }

@app.get("/validate/{symbol}", response_model=CompanyValidationResponse)
async def validate_company(symbol: str):
    """
    Validate company legitimacy and assess risk

    Args:
        symbol: Stock symbol to validate

    Returns:
        Company validation results with legitimacy score and risk assessment
    """
    try:
        logger.info(f"Validating company: {symbol}")

        # Validate company
        validation_result = company_validator.validate_company_legitimacy(symbol.upper())

        if not validation_result:
            raise HTTPException(status_code=404, detail=f"Could not validate company {symbol}")

        # Get risk assessment
        risk_assessment = company_validator.get_risk_assessment(symbol.upper())

        return CompanyValidationResponse(
            symbol=symbol.upper(),
            is_legitimate=validation_result.get('is_legitimate', False),
            legitimacy_score=validation_result.get('legitimacy_score', 0.0),
            risk_level=risk_assessment.get('risk_level', 'UNKNOWN'),
            red_flags=validation_result.get('red_flags', [])
        )

    except Exception as e:
        logger.error(f"Error validating company {symbol}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Validation failed: {str(e)}")

@app.get("/technical/{symbol}")
async def get_technical_analysis(
    symbol: str,
    period: str = Query("1y", description="Data period (1d, 5d, 1mo, 3mo, 6mo, 1y, 2y, 5y, 10y)"),
    interval: str = Query("1d", description="Data interval (1m, 2m, 5m, 15m, 30m, 60m, 90m, 1h, 1d, 5d, 1wk, 1mo)")
):
    """
    Get comprehensive technical analysis for a symbol

    Args:
        symbol: Stock symbol
        period: Data period
        interval: Data interval

    Returns:
        Technical analysis results including indicators, patterns, and trend analysis
    """
    try:
        logger.info(f"Performing technical analysis for {symbol}")

        # Get historical data
        df = data_collector.get_historical_data(symbol.upper(), period, interval)

        if df.empty:
            raise HTTPException(status_code=404, detail=f"No data found for symbol {symbol}")

        # Calculate technical indicators
        df_with_indicators = technical_analyzer.calculate_indicators(df)
        df_with_patterns = technical_analyzer.detect_candlestick_patterns(df_with_indicators)

        # Perform analysis
        trend_analysis = technical_analyzer.analyze_trend(df_with_patterns)
        pattern_summary = technical_analyzer.get_pattern_summary(df_with_patterns)
        support_resistance = technical_analyzer.detect_support_resistance(df_with_patterns)
        volatility = technical_analyzer.calculate_volatility(df_with_patterns)

        return {
            "symbol": symbol.upper(),
            "timestamp": datetime.now(),
            "data_points": len(df),
            "current_price": float(df['Close'].iloc[-1]),
            "trend_analysis": trend_analysis,
            "pattern_summary": pattern_summary,
            "support_resistance": support_resistance,
            "volatility": volatility,
            "latest_indicators": {
                "rsi": float(df_with_indicators['RSI'].iloc[-1]) if 'RSI' in df_with_indicators.columns else None,
                "macd": float(df_with_indicators['MACD'].iloc[-1]) if 'MACD' in df_with_indicators.columns else None,
                "sma_20": float(df_with_indicators['SMA_20'].iloc[-1]) if 'SMA_20' in df_with_indicators.columns else None,
                "sma_50": float(df_with_indicators['SMA_50'].iloc[-1]) if 'SMA_50' in df_with_indicators.columns else None
            }
        }

    except Exception as e:
        logger.error(f"Error in technical analysis for {symbol}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Technical analysis failed: {str(e)}")

@app.get("/signals/{symbol}", response_model=TradingSignalResponse)
async def get_trading_signals(
    symbol: str,
    period: str = Query("1y", description="Data period for analysis"),
    interval: str = Query("1d", description="Data interval")
):
    """
    Generate comprehensive trading signals for a symbol

    Args:
        symbol: Stock symbol
        period: Data period
        interval: Data interval

    Returns:
        Comprehensive trading signals with buy/sell recommendations
    """
    try:
        logger.info(f"Generating trading signals for {symbol}")

        # Get historical data
        df = data_collector.get_historical_data(symbol.upper(), period, interval)

        if df.empty:
            raise HTTPException(status_code=404, detail=f"No data found for symbol {symbol}")

        # Generate comprehensive signals
        signals = signal_generator.generate_comprehensive_signals(symbol.upper(), df)

        return TradingSignalResponse(
            symbol=signals['symbol'],
            primary_signal=signals['primary_signal'],
            confidence=signals['confidence'],
            risk_level=signals['risk_level'],
            entry_price=signals['entry_price'],
            stop_loss=signals['stop_loss'],
            take_profit=signals['take_profit'],
            reasoning=signals['reasoning']
        )

    except Exception as e:
        logger.error(f"Error generating signals for {symbol}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Signal generation failed: {str(e)}")

@app.get("/predict/{symbol}")
async def predict_price(
    symbol: str,
    days_ahead: int = Query(1, description="Number of days to predict ahead (1-5)"),
    period: str = Query("2y", description="Historical data period for training")
):
    """
    Predict future price using AI models

    Args:
        symbol: Stock symbol
        days_ahead: Number of days to predict ahead
        period: Historical data period

    Returns:
        Price predictions from multiple AI models
    """
    try:
        logger.info(f"Predicting price for {symbol}, {days_ahead} days ahead")

        if days_ahead < 1 or days_ahead > 5:
            raise HTTPException(status_code=400, detail="days_ahead must be between 1 and 5")

        # Get historical data
        df = data_collector.get_historical_data(symbol.upper(), period, "1d")

        if df.empty or len(df) < 100:
            raise HTTPException(status_code=404, detail=f"Insufficient data for prediction of {symbol}")

        # Train models and make predictions
        current_price = float(df['Close'].iloc[-1])

        # Train regression models
        training_results = ai_predictor.train_regression_models(df)

        # Get predictions
        price_predictions = ai_predictor.predict_price(df, days_ahead)
        trend_prediction = ai_predictor.predict_trend_direction(df)

        return {
            "symbol": symbol.upper(),
            "timestamp": datetime.now(),
            "current_price": current_price,
            "days_ahead": days_ahead,
            "price_predictions": price_predictions,
            "trend_prediction": trend_prediction,
            "model_performance": {k: {"r2": v["r2"], "rmse": v["rmse"]} for k, v in training_results.items()},
            "prediction_confidence": trend_prediction.get('confidence', 0.5)
        }

    except Exception as e:
        logger.error(f"Error predicting price for {symbol}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Price prediction failed: {str(e)}")

@app.get("/temporal/{symbol}")
async def get_temporal_analysis(
    symbol: str,
    period: str = Query("1y", description="Data period for analysis")
):
    """
    Get temporal analysis (yesterday, today, tomorrow trends)

    Args:
        symbol: Stock symbol
        period: Data period

    Returns:
        Temporal trend analysis across multiple timeframes
    """
    try:
        logger.info(f"Performing temporal analysis for {symbol}")

        # Get historical data
        df = data_collector.get_historical_data(symbol.upper(), period, "1d")

        if df.empty:
            raise HTTPException(status_code=404, detail=f"No data found for symbol {symbol}")

        # Perform temporal analysis
        temporal_analysis = timeframe_analyzer.analyze_temporal_trends(df, symbol.upper())

        return {
            "symbol": symbol.upper(),
            "timestamp": datetime.now(),
            "temporal_analysis": temporal_analysis
        }

    except Exception as e:
        logger.error(f"Error in temporal analysis for {symbol}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Temporal analysis failed: {str(e)}")

@app.post("/batch/analyze")
async def batch_analysis(request: BatchAnalysisRequest):
    """
    Perform batch analysis on multiple symbols

    Args:
        request: Batch analysis request with list of symbols

    Returns:
        Analysis results for all requested symbols
    """
    try:
        logger.info(f"Performing batch analysis for {len(request.symbols)} symbols")

        if len(request.symbols) > config.MAX_SYMBOLS_PER_REQUEST:
            raise HTTPException(
                status_code=400,
                detail=f"Too many symbols. Maximum allowed: {config.MAX_SYMBOLS_PER_REQUEST}"
            )

        # Collect data for all symbols
        data_dict = {}
        for symbol in request.symbols:
            try:
                df = data_collector.get_historical_data(symbol.upper(), request.period, request.interval)
                if not df.empty:
                    data_dict[symbol.upper()] = df
            except Exception as e:
                logger.warning(f"Failed to get data for {symbol}: {str(e)}")
                continue

        # Generate signals for all symbols
        batch_results = signal_generator.generate_batch_signals(list(data_dict.keys()), data_dict)

        return {
            "timestamp": datetime.now(),
            "symbols_analyzed": len(batch_results),
            "symbols_requested": len(request.symbols),
            "results": batch_results
        }

    except Exception as e:
        logger.error(f"Error in batch analysis: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Batch analysis failed: {str(e)}")

@app.get("/market/overview")
async def get_market_overview():
    """
    Get market overview with analysis of default symbols

    Returns:
        Market overview with signals for major stocks
    """
    try:
        logger.info("Generating market overview")

        # Analyze default symbols
        symbols = config.DEFAULT_SYMBOLS

        # Collect data
        data_dict = {}
        for symbol in symbols:
            try:
                df = data_collector.get_historical_data(symbol, "6mo", "1d")
                if not df.empty:
                    data_dict[symbol] = df
            except Exception as e:
                logger.warning(f"Failed to get data for {symbol}: {str(e)}")
                continue

        # Generate signals
        market_signals = signal_generator.generate_batch_signals(list(data_dict.keys()), data_dict)

        # Summarize market sentiment
        buy_signals = len([s for s in market_signals.values() if 'BUY' in s.get('primary_signal', '')])
        sell_signals = len([s for s in market_signals.values() if 'SELL' in s.get('primary_signal', '')])
        total_signals = len(market_signals)

        market_sentiment = "NEUTRAL"
        if buy_signals > sell_signals * 1.5:
            market_sentiment = "BULLISH"
        elif sell_signals > buy_signals * 1.5:
            market_sentiment = "BEARISH"

        return {
            "timestamp": datetime.now(),
            "market_sentiment": market_sentiment,
            "symbols_analyzed": total_signals,
            "buy_signals": buy_signals,
            "sell_signals": sell_signals,
            "neutral_signals": total_signals - buy_signals - sell_signals,
            "individual_signals": market_signals
        }

    except Exception as e:
        logger.error(f"Error generating market overview: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Market overview failed: {str(e)}")

# Live Trading Endpoints

@app.post("/scan")
async def market_scan(request: ScanRequest):
    """
    Scan market for trading opportunities

    Args:
        request: Scan request with scan type and parameters

    Returns:
        List of trading opportunities found by the scanner
    """
    try:
        logger.info(f"Starting {request.scan_type} market scan")

        # Perform market scan
        scan_results = await market_scanner.scan_for_opportunities(request.scan_type)

        # Limit results
        opportunities = scan_results.get('results', [])[:request.limit]

        return {
            "scan_type": request.scan_type,
            "timestamp": datetime.now(),
            "opportunities_found": len(opportunities),
            "symbols_scanned": scan_results.get('symbols_scanned', 0),
            "opportunities": opportunities
        }

    except Exception as e:
        logger.error(f"Error in market scan: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Market scan failed: {str(e)}")

@app.get("/scan/results")
async def get_scan_results(scan_type: str = Query(None, description="Specific scan type to retrieve")):
    """Get latest scan results"""
    try:
        if scan_type:
            results = market_scanner.get_scan_results(scan_type)
        else:
            results = market_scanner.get_scan_results()

        return {
            "timestamp": datetime.now(),
            "scan_results": results
        }

    except Exception as e:
        logger.error(f"Error retrieving scan results: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to retrieve scan results: {str(e)}")

@app.get("/scan/top-opportunities")
async def get_top_opportunities(limit: int = Query(10, description="Number of top opportunities to return")):
    """Get top opportunities across all scan types"""
    try:
        opportunities = market_scanner.get_top_opportunities(limit)

        return {
            "timestamp": datetime.now(),
            "top_opportunities": opportunities,
            "count": len(opportunities)
        }

    except Exception as e:
        logger.error(f"Error getting top opportunities: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get opportunities: {str(e)}")

@app.post("/live/monitor")
async def start_live_monitoring(request: LiveMonitoringRequest, background_tasks: BackgroundTasks):
    """
    Start live monitoring for specified symbols

    Args:
        request: Live monitoring request with symbols and settings

    Returns:
        Confirmation of monitoring start
    """
    try:
        logger.info(f"Starting live monitoring for {len(request.symbols)} symbols")

        # Start monitoring in background
        background_tasks.add_task(
            live_trading_engine.start_live_monitoring,
            request.symbols
        )

        return {
            "message": "Live monitoring started",
            "symbols": request.symbols,
            "monitoring_enabled": True,
            "alerts_enabled": request.enable_alerts,
            "timestamp": datetime.now()
        }

    except Exception as e:
        logger.error(f"Error starting live monitoring: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to start monitoring: {str(e)}")

@app.get("/live/status")
async def get_live_status():
    """Get live monitoring status"""
    try:
        market_status = live_trading_engine.get_market_status()
        current_signals = live_trading_engine.get_current_signals()

        return {
            "timestamp": datetime.now(),
            "is_monitoring": live_trading_engine.is_running,
            "market_status": market_status,
            "monitored_symbols": len(current_signals),
            "current_signals": current_signals
        }

    except Exception as e:
        logger.error(f"Error getting live status: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get status: {str(e)}")

@app.post("/live/alert")
async def add_price_alert(request: PriceAlertRequest):
    """Add price alert for a symbol"""
    try:
        live_trading_engine.add_price_alert(
            request.symbol,
            request.condition,
            request.price,
            request.message
        )

        return {
            "message": f"Price alert added for {request.symbol}",
            "symbol": request.symbol,
            "condition": request.condition,
            "price": request.price,
            "timestamp": datetime.now()
        }

    except Exception as e:
        logger.error(f"Error adding price alert: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to add alert: {str(e)}")

@app.get("/live/data/{symbol}")
async def get_live_data(symbol: str, minutes: int = Query(60, description="Minutes of historical data")):
    """Get live data for a symbol"""
    try:
        live_data = live_trading_engine.get_live_data(symbol, minutes)

        return {
            "symbol": symbol,
            "timestamp": datetime.now(),
            "data_points": len(live_data),
            "minutes_requested": minutes,
            "live_data": live_data
        }

    except Exception as e:
        logger.error(f"Error getting live data for {symbol}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get live data: {str(e)}")

# Portfolio Management Endpoints

@app.post("/portfolio/position")
async def add_position(request: PositionRequest):
    """Add a new position to the portfolio"""
    try:
        position_id = portfolio_tracker.add_position(
            symbol=request.symbol,
            position_type=request.position_type,
            entry_price=request.entry_price,
            quantity=request.quantity,
            stop_loss=request.stop_loss,
            take_profit=request.take_profit,
            notes=request.notes
        )

        return {
            "message": "Position added successfully",
            "position_id": position_id,
            "symbol": request.symbol,
            "type": request.position_type,
            "quantity": request.quantity,
            "entry_price": request.entry_price,
            "timestamp": datetime.now()
        }

    except Exception as e:
        logger.error(f"Error adding position: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to add position: {str(e)}")

@app.delete("/portfolio/position/{position_id}")
async def close_position(position_id: str, exit_price: float, quantity: int = None, notes: str = ""):
    """Close a position (fully or partially)"""
    try:
        result = portfolio_tracker.close_position(position_id, exit_price, quantity, notes)

        return {
            "message": "Position closed successfully",
            "position_id": position_id,
            "pnl": result['pnl'],
            "exit_price": exit_price,
            "quantity_closed": result['quantity_closed'],
            "remaining_quantity": result['remaining_quantity'],
            "timestamp": datetime.now()
        }

    except Exception as e:
        logger.error(f"Error closing position: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to close position: {str(e)}")

@app.get("/portfolio/summary")
async def get_portfolio_summary():
    """Get portfolio summary and performance metrics"""
    try:
        # Update positions with current prices
        portfolio_tracker.update_positions()

        performance_metrics = portfolio_tracker.get_performance_metrics()
        position_summary = portfolio_tracker.get_position_summary()
        risk_metrics = portfolio_tracker.get_risk_metrics()

        return {
            "timestamp": datetime.now(),
            "performance_metrics": performance_metrics,
            "positions": position_summary,
            "risk_metrics": risk_metrics
        }

    except Exception as e:
        logger.error(f"Error getting portfolio summary: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get portfolio summary: {str(e)}")

@app.get("/portfolio/positions")
async def get_positions():
    """Get all open positions"""
    try:
        positions = portfolio_tracker.get_position_summary()

        return {
            "timestamp": datetime.now(),
            "open_positions": len(positions),
            "positions": positions
        }

    except Exception as e:
        logger.error(f"Error getting positions: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get positions: {str(e)}")

@app.get("/portfolio/history")
async def get_trade_history(limit: int = Query(50, description="Number of trades to return")):
    """Get trade history"""
    try:
        history = portfolio_tracker.get_trade_history(limit)

        return {
            "timestamp": datetime.now(),
            "trade_count": len(history),
            "trade_history": history
        }

    except Exception as e:
        logger.error(f"Error getting trade history: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get trade history: {str(e)}")

@app.get("/portfolio/pnl")
async def get_daily_pnl(days: int = Query(30, description="Number of days to retrieve")):
    """Get daily P&L for the specified period"""
    try:
        daily_pnl = portfolio_tracker.get_daily_pnl(days)

        return {
            "timestamp": datetime.now(),
            "period_days": days,
            "daily_pnl": daily_pnl
        }

    except Exception as e:
        logger.error(f"Error getting daily P&L: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get daily P&L: {str(e)}")

# AI-Powered Analysis Endpoints

@app.post("/ai/analyze")
async def ai_trend_analysis(request: AIAnalysisRequest):
    """
    Advanced AI-powered trend analysis with buy/sell suggestions and profit predictions

    Args:
        request: AI analysis request with symbol and parameters

    Returns:
        Comprehensive AI analysis with predictions and profit potential
    """
    try:
        logger.info(f"Starting AI analysis for {request.symbol}")

        # Perform AI trend analysis
        analysis = await ai_trend_analyzer.analyze_market_trends(
            request.symbol,
            request.timeframe
        )

        # Create notifications if requested
        if request.include_notifications and 'error' not in analysis:
            await notification_system.create_profit_alert(
                request.symbol,
                analysis,
                user_id="default"
            )

        return {
            "symbol": request.symbol,
            "timestamp": datetime.now(),
            "analysis": analysis,
            "notification_created": request.include_notifications and 'error' not in analysis
        }

    except Exception as e:
        logger.error(f"Error in AI analysis for {request.symbol}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"AI analysis failed: {str(e)}")

@app.get("/ai/suggestions/{symbol}")
async def get_ai_suggestions(symbol: str, timeframe: str = Query("1d", description="Analysis timeframe")):
    """Get AI-powered buy/sell suggestions with profit predictions"""
    try:
        analysis = await ai_trend_analyzer.analyze_market_trends(symbol.upper(), timeframe)

        if 'error' in analysis:
            raise HTTPException(status_code=404, detail=analysis['error'])

        return {
            "symbol": symbol.upper(),
            "timestamp": datetime.now(),
            "suggestions": analysis.get('suggestions', {}),
            "profit_potential": analysis.get('profit_potential', {}),
            "confidence_score": analysis.get('confidence_score', 0),
            "risk_level": analysis.get('risk_level', 'UNKNOWN'),
            "market_sentiment": analysis.get('market_sentiment', 'NEUTRAL')
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting AI suggestions for {symbol}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get suggestions: {str(e)}")

# Company Search Endpoints

@app.post("/search/companies")
async def search_companies(request: CompanySearchRequest):
    """
    Search for companies by name, symbol, or keyword

    Args:
        request: Search request with query and limit

    Returns:
        List of matching companies with basic information
    """
    try:
        logger.info(f"Searching companies: {request.query}")

        results = await company_search_engine.search_companies(
            request.query,
            request.limit
        )

        return {
            "query": request.query,
            "timestamp": datetime.now(),
            "results_count": len(results),
            "companies": results
        }

    except Exception as e:
        logger.error(f"Error searching companies: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Search failed: {str(e)}")

@app.get("/company/{symbol}")
async def get_company_details(symbol: str):
    """
    Get comprehensive company details including live trade data

    Args:
        symbol: Stock symbol

    Returns:
        Comprehensive company information with live data
    """
    try:
        logger.info(f"Getting company details for {symbol}")

        company_data = await company_search_engine.get_company_details(symbol.upper())

        if 'error' in company_data:
            raise HTTPException(status_code=404, detail=company_data['error'])

        return {
            "symbol": symbol.upper(),
            "timestamp": datetime.now(),
            "company_data": company_data
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting company details for {symbol}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get company details: {str(e)}")

@app.get("/company/{symbol}/live")
async def get_live_trade_data(symbol: str):
    """
    Get real-time trading data for a symbol

    Args:
        symbol: Stock symbol

    Returns:
        Live trading data with current prices and statistics
    """
    try:
        logger.info(f"Getting live trade data for {symbol}")

        live_data = await company_search_engine.get_live_trade_data(symbol.upper())

        if 'error' in live_data:
            raise HTTPException(status_code=404, detail=live_data['error'])

        return {
            "symbol": symbol.upper(),
            "timestamp": datetime.now(),
            "live_data": live_data
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting live trade data for {symbol}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get live data: {str(e)}")

@app.get("/trending")
async def get_trending_stocks(limit: int = Query(20, description="Number of trending stocks to return")):
    """Get trending/popular stocks with current data"""
    try:
        trending_stocks = await company_search_engine.get_trending_stocks(limit)

        return {
            "timestamp": datetime.now(),
            "trending_count": len(trending_stocks),
            "trending_stocks": trending_stocks
        }

    except Exception as e:
        logger.error(f"Error getting trending stocks: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get trending stocks: {str(e)}")

# Notification Endpoints

@app.get("/notifications")
async def get_notifications(
    user_id: str = Query("default", description="User ID"),
    limit: int = Query(50, description="Number of notifications to return"),
    unread_only: bool = Query(False, description="Return only unread notifications")
):
    """Get user notifications"""
    try:
        notifications = notification_system.get_notifications(user_id, limit, unread_only)

        return {
            "user_id": user_id,
            "timestamp": datetime.now(),
            "notifications_count": len(notifications),
            "notifications": notifications
        }

    except Exception as e:
        logger.error(f"Error getting notifications: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get notifications: {str(e)}")

@app.post("/notifications/{notification_id}/read")
async def mark_notification_read(notification_id: str):
    """Mark notification as read"""
    try:
        success = notification_system.mark_notification_read(notification_id)

        if not success:
            raise HTTPException(status_code=404, detail="Notification not found")

        return {
            "notification_id": notification_id,
            "marked_read": True,
            "timestamp": datetime.now()
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error marking notification as read: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to mark notification: {str(e)}")

@app.get("/notifications/stats")
async def get_notification_stats():
    """Get notification statistics"""
    try:
        stats = notification_system.get_notification_stats()

        return {
            "timestamp": datetime.now(),
            "stats": stats
        }

    except Exception as e:
        logger.error(f"Error getting notification stats: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get stats: {str(e)}")

# WebSocket endpoint for live updates
from fastapi import WebSocket, WebSocketDisconnect

@app.websocket("/ws/live")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket endpoint for live trading updates and notifications"""
    await websocket.accept()
    user_id = "default"  # In production, extract from authentication

    # Subscribe to both trading engine and notification system
    live_trading_engine.websocket_connections.add(websocket)
    notification_system.subscribe_websocket(websocket, user_id)

    try:
        # Send initial data
        initial_data = {
            'type': 'connection_established',
            'market_status': live_trading_engine.get_market_status(),
            'current_signals': live_trading_engine.get_current_signals(),
            'notifications': notification_system.get_notifications(user_id, limit=10, unread_only=True),
            'timestamp': datetime.now().isoformat()
        }
        await websocket.send_json(initial_data)

        # Keep connection alive and handle messages
        while True:
            try:
                data = await websocket.receive_json()

                # Handle different message types
                message_type = data.get('type')

                if message_type == 'request_ai_analysis':
                    symbol = data.get('symbol')
                    if symbol:
                        # Perform AI analysis and send result
                        analysis = await ai_trend_analyzer.analyze_market_trends(symbol)
                        response = {
                            'type': 'ai_analysis_result',
                            'symbol': symbol,
                            'analysis': analysis,
                            'timestamp': datetime.now().isoformat()
                        }
                        await websocket.send_json(response)

                elif message_type == 'search_company':
                    query = data.get('query')
                    if query:
                        # Perform company search and send results
                        results = await company_search_engine.search_companies(query, limit=10)
                        response = {
                            'type': 'search_results',
                            'query': query,
                            'results': results,
                            'timestamp': datetime.now().isoformat()
                        }
                        await websocket.send_json(response)

                else:
                    # Handle other messages through trading engine
                    await live_trading_engine.handle_client_message(websocket, data)

            except WebSocketDisconnect:
                break
            except Exception as e:
                logger.error(f"WebSocket error: {str(e)}")
                break

    except WebSocketDisconnect:
        pass
    finally:
        live_trading_engine.websocket_connections.discard(websocket)
        notification_system.unsubscribe_websocket(websocket, user_id)

# Startup event
@app.on_event("startup")
async def startup_event():
    """Initialize services on startup"""
    logger.info("Starting AI Trading Analysis System services")

    # Start notification service
    await notification_system.start_notification_service()

    logger.info("All services started successfully")

@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup on shutdown"""
    logger.info("Shutting down AI Trading Analysis System")

    # Stop services
    notification_system.stop_service()
    live_trading_engine.stop_monitoring()

    logger.info("All services stopped")

if __name__ == "__main__":
    logger.info("Starting AI Trading Analysis System")
    uvicorn.run(
        "main:app",
        host=config.HOST,
        port=config.PORT,
        reload=config.DEBUG,
        log_level=config.LOG_LEVEL.lower()
    )