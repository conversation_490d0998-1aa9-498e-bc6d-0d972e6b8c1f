"""
Advanced Company Search System with Live Trade Data and Comprehensive Information
"""
import asyncio
import aiohttp
import pandas as pd
import yfinance as yf
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from loguru import logger
import requests
import json
from concurrent.futures import Thread<PERSON>oolExecutor
import re

from config import Config
from utils.technical_analysis import TechnicalAnalyzer

class CompanySearchEngine:
    def __init__(self):
        self.config = Config()
        self.technical_analyzer = TechnicalAnalyzer()
        self.search_cache = {}
        self.company_cache = {}
        
        # Popular exchanges and their symbols
        self.exchanges = {
            'NASDAQ': 'NASDAQ',
            'NYSE': 'NYSE', 
            'AMEX': 'AMEX',
            'OTC': 'OTC'
        }
        
    async def search_companies(self, query: str, limit: int = 20) -> List[Dict]:
        """
        Search for companies by name, symbol, or keyword
        
        Args:
            query: Search query (company name, symbol, or keyword)
            limit: Maximum number of results to return
            
        Returns:
            List of matching companies with basic information
        """
        try:
            logger.info(f"Searching for companies: {query}")
            
            # Check cache first
            cache_key = f"search_{query.lower()}_{limit}"
            if cache_key in self.search_cache:
                cached_result, cache_time = self.search_cache[cache_key]
                if datetime.now() - cache_time < timedelta(hours=1):
                    return cached_result
            
            # Perform search
            results = await self._perform_company_search(query, limit)
            
            # Cache results
            self.search_cache[cache_key] = (results, datetime.now())
            
            return results
            
        except Exception as e:
            logger.error(f"Error searching companies: {str(e)}")
            return []
    
    async def get_company_details(self, symbol: str) -> Dict:
        """
        Get comprehensive company details including live trade data
        
        Args:
            symbol: Stock symbol
            
        Returns:
            Comprehensive company information
        """
        try:
            logger.info(f"Getting company details for {symbol}")
            
            # Check cache
            cache_key = f"details_{symbol.upper()}"
            if cache_key in self.company_cache:
                cached_result, cache_time = self.company_cache[cache_key]
                if datetime.now() - cache_time < timedelta(minutes=5):
                    return cached_result
            
            # Get comprehensive data
            company_info = await self._get_comprehensive_company_data(symbol)
            
            # Cache results
            self.company_cache[cache_key] = (company_info, datetime.now())
            
            return company_info
            
        except Exception as e:
            logger.error(f"Error getting company details for {symbol}: {str(e)}")
            return self._create_error_response(symbol, str(e))
    
    async def get_live_trade_data(self, symbol: str) -> Dict:
        """
        Get real-time trading data for a symbol
        
        Args:
            symbol: Stock symbol
            
        Returns:
            Live trading data with technical analysis
        """
        try:
            ticker = yf.Ticker(symbol)
            
            # Get current data
            info = ticker.info
            hist = ticker.history(period="1d", interval="1m")
            
            if hist.empty:
                hist = ticker.history(period="5d", interval="1d")
            
            current_price = info.get('currentPrice', 0)
            if current_price == 0 and not hist.empty:
                current_price = hist['Close'].iloc[-1]
            
            # Calculate intraday statistics
            if not hist.empty:
                day_high = hist['High'].max()
                day_low = hist['Low'].min()
                day_open = hist['Open'].iloc[0]
                day_volume = hist['Volume'].sum()
                
                # Calculate price changes
                price_change = current_price - day_open
                price_change_pct = (price_change / day_open * 100) if day_open > 0 else 0
                
                # Get previous close for comparison
                prev_close = info.get('previousClose', day_open)
                change_from_prev = current_price - prev_close
                change_from_prev_pct = (change_from_prev / prev_close * 100) if prev_close > 0 else 0
            else:
                day_high = day_low = day_open = current_price
                day_volume = 0
                price_change = price_change_pct = 0
                change_from_prev = change_from_prev_pct = 0
            
            # Get bid/ask data
            bid_price = info.get('bid', current_price)
            ask_price = info.get('ask', current_price)
            bid_size = info.get('bidSize', 0)
            ask_size = info.get('askSize', 0)
            
            # Calculate spread
            spread = ask_price - bid_price if ask_price > bid_price else 0
            spread_pct = (spread / current_price * 100) if current_price > 0 else 0
            
            return {
                'symbol': symbol.upper(),
                'timestamp': datetime.now().isoformat(),
                'current_price': float(current_price),
                'day_change': float(change_from_prev),
                'day_change_percent': float(change_from_prev_pct),
                'intraday_change': float(price_change),
                'intraday_change_percent': float(price_change_pct),
                'day_high': float(day_high),
                'day_low': float(day_low),
                'day_open': float(day_open),
                'previous_close': float(prev_close),
                'volume': int(day_volume),
                'average_volume': int(info.get('averageVolume', 0)),
                'market_cap': info.get('marketCap', 0),
                'bid_price': float(bid_price),
                'ask_price': float(ask_price),
                'bid_size': int(bid_size),
                'ask_size': int(ask_size),
                'spread': float(spread),
                'spread_percent': float(spread_pct),
                'trading_status': self._determine_trading_status(info),
                'last_trade_time': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error getting live trade data for {symbol}: {str(e)}")
            return {'symbol': symbol, 'error': str(e)}
    
    async def _perform_company_search(self, query: str, limit: int) -> List[Dict]:
        """Perform the actual company search"""
        try:
            results = []
            
            # Method 1: Direct symbol lookup
            if len(query) <= 5 and query.isalpha():
                direct_result = await self._try_direct_symbol_lookup(query.upper())
                if direct_result:
                    results.append(direct_result)
            
            # Method 2: Search popular stocks
            popular_matches = await self._search_popular_stocks(query)
            results.extend(popular_matches)
            
            # Method 3: Yahoo Finance search
            yahoo_matches = await self._search_yahoo_finance(query)
            results.extend(yahoo_matches)
            
            # Remove duplicates and limit results
            seen_symbols = set()
            unique_results = []
            
            for result in results:
                symbol = result.get('symbol', '').upper()
                if symbol and symbol not in seen_symbols:
                    seen_symbols.add(symbol)
                    unique_results.append(result)
                    
                    if len(unique_results) >= limit:
                        break
            
            return unique_results
            
        except Exception as e:
            logger.error(f"Error in company search: {str(e)}")
            return []
    
    async def _try_direct_symbol_lookup(self, symbol: str) -> Optional[Dict]:
        """Try direct symbol lookup"""
        try:
            ticker = yf.Ticker(symbol)
            info = ticker.info
            
            if info and info.get('longName'):
                return {
                    'symbol': symbol,
                    'name': info.get('longName', ''),
                    'sector': info.get('sector', ''),
                    'industry': info.get('industry', ''),
                    'exchange': info.get('exchange', ''),
                    'market_cap': info.get('marketCap', 0),
                    'current_price': info.get('currentPrice', 0),
                    'currency': info.get('currency', 'USD'),
                    'country': info.get('country', ''),
                    'website': info.get('website', ''),
                    'match_type': 'direct_symbol'
                }
            
            return None
            
        except Exception:
            return None
    
    async def _search_popular_stocks(self, query: str) -> List[Dict]:
        """Search in popular stocks list"""
        try:
            # Popular stocks database (simplified)
            popular_stocks = {
                'AAPL': 'Apple Inc.',
                'GOOGL': 'Alphabet Inc.',
                'MSFT': 'Microsoft Corporation',
                'AMZN': 'Amazon.com Inc.',
                'TSLA': 'Tesla Inc.',
                'META': 'Meta Platforms Inc.',
                'NVDA': 'NVIDIA Corporation',
                'NFLX': 'Netflix Inc.',
                'ADBE': 'Adobe Inc.',
                'CRM': 'Salesforce Inc.',
                'PYPL': 'PayPal Holdings Inc.',
                'INTC': 'Intel Corporation',
                'AMD': 'Advanced Micro Devices Inc.',
                'ORCL': 'Oracle Corporation',
                'IBM': 'International Business Machines',
                'CSCO': 'Cisco Systems Inc.',
                'JPM': 'JPMorgan Chase & Co.',
                'BAC': 'Bank of America Corp',
                'WMT': 'Walmart Inc.',
                'JNJ': 'Johnson & Johnson',
                'PG': 'Procter & Gamble Co',
                'KO': 'The Coca-Cola Company',
                'PEP': 'PepsiCo Inc.',
                'DIS': 'The Walt Disney Company',
                'NKE': 'NIKE Inc.',
                'MCD': 'McDonald\'s Corporation'
            }
            
            query_lower = query.lower()
            matches = []
            
            for symbol, name in popular_stocks.items():
                if (query_lower in symbol.lower() or 
                    query_lower in name.lower() or
                    any(word in name.lower() for word in query_lower.split())):
                    
                    # Get additional info
                    try:
                        ticker = yf.Ticker(symbol)
                        info = ticker.info
                        
                        matches.append({
                            'symbol': symbol,
                            'name': name,
                            'sector': info.get('sector', ''),
                            'industry': info.get('industry', ''),
                            'exchange': info.get('exchange', ''),
                            'market_cap': info.get('marketCap', 0),
                            'current_price': info.get('currentPrice', 0),
                            'currency': info.get('currency', 'USD'),
                            'match_type': 'popular_stock'
                        })
                    except Exception:
                        matches.append({
                            'symbol': symbol,
                            'name': name,
                            'match_type': 'popular_stock'
                        })
            
            return matches[:10]  # Limit popular stock matches
            
        except Exception as e:
            logger.error(f"Error searching popular stocks: {str(e)}")
            return []
    
    async def _search_yahoo_finance(self, query: str) -> List[Dict]:
        """Search using Yahoo Finance API"""
        try:
            # This is a simplified implementation
            # In production, you might use a proper financial data API
            
            # Try to search for tickers that might match
            potential_symbols = []
            
            # Generate potential symbols from query
            if query.isalpha():
                potential_symbols = [
                    query.upper(),
                    query.upper() + '.TO',  # Toronto
                    query.upper() + '.L',   # London
                    query.upper() + '.F',   # Frankfurt
                ]
            
            results = []
            
            for symbol in potential_symbols:
                try:
                    ticker = yf.Ticker(symbol)
                    info = ticker.info
                    
                    if info and info.get('longName'):
                        results.append({
                            'symbol': symbol,
                            'name': info.get('longName', ''),
                            'sector': info.get('sector', ''),
                            'industry': info.get('industry', ''),
                            'exchange': info.get('exchange', ''),
                            'market_cap': info.get('marketCap', 0),
                            'current_price': info.get('currentPrice', 0),
                            'currency': info.get('currency', 'USD'),
                            'country': info.get('country', ''),
                            'match_type': 'yahoo_search'
                        })
                except Exception:
                    continue
            
            return results
            
        except Exception as e:
            logger.error(f"Error in Yahoo Finance search: {str(e)}")
            return []
    
    async def _get_comprehensive_company_data(self, symbol: str) -> Dict:
        """Get comprehensive company data"""
        try:
            ticker = yf.Ticker(symbol)
            
            # Get all available data
            info = ticker.info
            hist = ticker.history(period="1y", interval="1d")
            
            # Basic company information
            company_data = {
                'symbol': symbol.upper(),
                'name': info.get('longName', ''),
                'sector': info.get('sector', ''),
                'industry': info.get('industry', ''),
                'exchange': info.get('exchange', ''),
                'currency': info.get('currency', 'USD'),
                'country': info.get('country', ''),
                'website': info.get('website', ''),
                'business_summary': info.get('longBusinessSummary', ''),
                'employees': info.get('fullTimeEmployees', 0),
                'founded': info.get('founded', ''),
                'headquarters': f"{info.get('city', '')}, {info.get('state', '')}, {info.get('country', '')}".strip(', '),
            }
            
            # Financial metrics
            company_data['financial_metrics'] = {
                'market_cap': info.get('marketCap', 0),
                'enterprise_value': info.get('enterpriseValue', 0),
                'revenue': info.get('totalRevenue', 0),
                'revenue_growth': info.get('revenueGrowth', 0),
                'gross_profit': info.get('grossProfits', 0),
                'operating_income': info.get('operatingCashflow', 0),
                'net_income': info.get('netIncomeToCommon', 0),
                'total_cash': info.get('totalCash', 0),
                'total_debt': info.get('totalDebt', 0),
                'free_cashflow': info.get('freeCashflow', 0)
            }
            
            # Valuation ratios
            company_data['valuation_ratios'] = {
                'pe_ratio': info.get('trailingPE', 0),
                'forward_pe': info.get('forwardPE', 0),
                'peg_ratio': info.get('pegRatio', 0),
                'price_to_book': info.get('priceToBook', 0),
                'price_to_sales': info.get('priceToSalesTrailing12Months', 0),
                'ev_to_revenue': info.get('enterpriseToRevenue', 0),
                'ev_to_ebitda': info.get('enterpriseToEbitda', 0)
            }
            
            # Profitability metrics
            company_data['profitability_metrics'] = {
                'profit_margins': info.get('profitMargins', 0),
                'operating_margins': info.get('operatingMargins', 0),
                'gross_margins': info.get('grossMargins', 0),
                'return_on_equity': info.get('returnOnEquity', 0),
                'return_on_assets': info.get('returnOnAssets', 0)
            }
            
            # Stock performance
            if not hist.empty:
                current_price = hist['Close'].iloc[-1]
                year_high = hist['High'].max()
                year_low = hist['Low'].min()
                year_start_price = hist['Close'].iloc[0]
                
                company_data['stock_performance'] = {
                    'current_price': float(current_price),
                    '52_week_high': float(year_high),
                    '52_week_low': float(year_low),
                    'year_to_date_return': float((current_price - year_start_price) / year_start_price * 100),
                    'distance_from_high': float((year_high - current_price) / year_high * 100),
                    'distance_from_low': float((current_price - year_low) / year_low * 100)
                }
            
            # Get live trade data
            company_data['live_trade_data'] = await self.get_live_trade_data(symbol)
            
            # Technical analysis
            if not hist.empty and len(hist) > 50:
                df_with_indicators = self.technical_analyzer.calculate_indicators(hist)
                trend_analysis = self.technical_analyzer.analyze_trend(df_with_indicators)
                volatility = self.technical_analyzer.calculate_volatility(df_with_indicators)
                
                company_data['technical_analysis'] = {
                    'trend_analysis': trend_analysis,
                    'volatility': volatility,
                    'support_resistance': self.technical_analyzer.detect_support_resistance(df_with_indicators)
                }
            
            # Dividend information
            company_data['dividend_info'] = {
                'dividend_yield': info.get('dividendYield', 0),
                'dividend_rate': info.get('dividendRate', 0),
                'payout_ratio': info.get('payoutRatio', 0),
                'ex_dividend_date': info.get('exDividendDate', ''),
                'dividend_date': info.get('dividendDate', '')
            }
            
            # Risk metrics
            company_data['risk_metrics'] = {
                'beta': info.get('beta', 0),
                'debt_to_equity': info.get('debtToEquity', 0),
                'current_ratio': info.get('currentRatio', 0),
                'quick_ratio': info.get('quickRatio', 0)
            }
            
            # Add timestamp
            company_data['last_updated'] = datetime.now().isoformat()
            
            return company_data
            
        except Exception as e:
            logger.error(f"Error getting comprehensive company data: {str(e)}")
            return self._create_error_response(symbol, str(e))
    
    def _determine_trading_status(self, info: Dict) -> str:
        """Determine current trading status"""
        try:
            # This is simplified - in production you'd check market hours
            exchange = info.get('exchange', '')
            
            if exchange in ['NMS', 'NYQ', 'ASE']:  # Major US exchanges
                now = datetime.now()
                if now.weekday() < 5:  # Monday to Friday
                    current_time = now.time()
                    market_open = datetime.strptime('09:30', '%H:%M').time()
                    market_close = datetime.strptime('16:00', '%H:%M').time()
                    
                    if market_open <= current_time <= market_close:
                        return 'OPEN'
                    else:
                        return 'CLOSED'
                else:
                    return 'CLOSED'
            else:
                return 'UNKNOWN'
                
        except Exception:
            return 'UNKNOWN'
    
    def _create_error_response(self, symbol: str, error: str) -> Dict:
        """Create error response"""
        return {
            'symbol': symbol,
            'error': error,
            'timestamp': datetime.now().isoformat()
        }
    
    async def get_trending_stocks(self, limit: int = 20) -> List[Dict]:
        """Get trending/popular stocks"""
        try:
            # This would typically come from a financial data API
            # For now, return popular stocks with current data
            
            popular_symbols = [
                'AAPL', 'GOOGL', 'MSFT', 'AMZN', 'TSLA', 'META', 'NVDA', 'NFLX',
                'ADBE', 'CRM', 'PYPL', 'INTC', 'AMD', 'ORCL', 'IBM', 'CSCO',
                'JPM', 'BAC', 'WMT', 'JNJ', 'PG', 'KO', 'PEP', 'DIS'
            ]
            
            trending_stocks = []
            
            for symbol in popular_symbols[:limit]:
                try:
                    live_data = await self.get_live_trade_data(symbol)
                    if 'error' not in live_data:
                        trending_stocks.append(live_data)
                except Exception:
                    continue
            
            # Sort by volume or price change
            trending_stocks.sort(key=lambda x: abs(x.get('day_change_percent', 0)), reverse=True)
            
            return trending_stocks
            
        except Exception as e:
            logger.error(f"Error getting trending stocks: {str(e)}")
            return []
    
    def clear_cache(self):
        """Clear search and company cache"""
        self.search_cache.clear()
        self.company_cache.clear()
        logger.info("Search cache cleared")
