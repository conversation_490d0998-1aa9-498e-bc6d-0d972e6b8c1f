# 🚀 AI-Powered Trading Analysis System

**The Ultimate AI Trading Platform** - A comprehensive backend system that utilizes advanced artificial intelligence to analyze live trading data, provide buy/sell suggestions with profit predictions, send real-time notifications, and offer complete company search capabilities with live trade data.

## 🌟 **NEW ENHANCED FEATURES**

### 🤖 **Advanced AI Analysis Engine**
- **Smart Buy/Sell Suggestions** with confidence scoring
- **Profit Prediction Models** showing potential returns for different investment amounts
- **Risk Assessment** with detailed risk level analysis
- **Market Sentiment Analysis** using multiple AI models
- **Real-time Trend Prediction** with 1-day, 3-day, and 1-week forecasts

### 🔔 **Intelligent Notification System**
- **Pop-up Profit Alerts** showing potential profits in real-time
- **Signal Change Notifications** with instant WebSocket delivery
- **Price Target Alerts** with customizable conditions
- **Risk Warnings** for high-risk investments
- **Market Opportunity Alerts** from automated scanning

### 🔍 **Advanced Company Search**
- **Universal Company Search** - Find any company by name, symbol, or keyword
- **Live Trade Data** with real-time prices, volume, and market statistics
- **Comprehensive Company Profiles** with financial metrics and analysis
- **Technical Analysis Integration** for searched companies
- **Market Status Tracking** with trading hours detection

## Features

### 🔴 **NEW: Live Trading Engine**
- **Real-time Monitoring**: Live price tracking and signal generation
- **WebSocket Streaming**: Real-time updates via WebSocket connections
- **Price Alerts**: Customizable price and percentage change alerts
- **Live Dashboard**: Interactive web dashboard for monitoring
- **Market Scanner**: Real-time scanning for trading opportunities

### 🤖 AI-Powered Analysis
- **Machine Learning Models**: Random Forest, XGBoost, LightGBM, and LSTM neural networks
- **Price Prediction**: Multi-day ahead price forecasting with confidence intervals
- **Trend Direction**: Binary classification for up/down movement prediction
- **Pattern Recognition**: Advanced candlestick pattern detection and analysis

### 📊 Technical Analysis
- **50+ Technical Indicators**: RSI, MACD, Bollinger Bands, Stochastic, Williams %R, CCI, ATR
- **Candlestick Patterns**: Hammer, Doji, Engulfing, Morning/Evening Star, Three White Soldiers/Black Crows
- **Support/Resistance**: Dynamic level detection with pivot points
- **Trend Analysis**: Multi-timeframe trend identification and strength measurement

### 🏢 Company Validation
- **Legitimacy Scoring**: Comprehensive company validation with 0-1 scoring system
- **Risk Assessment**: Multi-factor risk analysis including market cap, volume, financials
- **Regulatory Compliance**: Basic compliance checks and red flag detection
- **Penny Stock Detection**: Automatic identification of high-risk penny stocks

### ⏰ Temporal Analysis
- **Yesterday's Analysis**: Complete analysis of previous trading session
- **Today's Monitoring**: Real-time analysis of current market conditions
- **Tomorrow's Prediction**: AI-powered forecasting for next trading session
- **Trend Correlation**: Cross-timeframe trend correlation analysis

### 📈 Trading Signals
- **Comprehensive Signals**: BUY, SELL, HOLD with confidence levels
- **Risk-Adjusted Recommendations**: Position sizing based on risk assessment
- **Stop Loss/Take Profit**: Automatic calculation of exit points
- **Multi-Source Validation**: Signals validated across technical, AI, and temporal analysis

### 🎯 **NEW: Market Scanner**
- **Breakout Scanner**: Find stocks breaking resistance levels
- **Oversold Scanner**: Identify potential bounce opportunities
- **Momentum Scanner**: Detect strong trending stocks
- **Gap Scanner**: Find significant gap up/down opportunities
- **Volume Scanner**: Identify unusual volume spikes

### 💼 **NEW: Portfolio Tracker**
- **Position Management**: Track long/short positions with P&L
- **Performance Metrics**: Win rate, profit factor, drawdown analysis
- **Risk Management**: Position sizing and risk assessment
- **Trade History**: Complete audit trail of all trades

## Installation

### Prerequisites
- Python 3.8+
- pip package manager

### Setup

1. **Clone the repository**
```bash
git clone <repository-url>
cd ai-trading-analysis
```

2. **Install dependencies**
```bash
pip install -r requirements.txt
```

3. **Configure API Keys**
```bash
cp .env.example .env
# Edit .env file with your API keys
```

Required API keys:
- **Alpha Vantage**: Get from https://www.alphavantage.co/support/#api-key
- **Finnhub**: Get from https://finnhub.io/
- **Quandl**: Get from https://www.quandl.com/ (optional)

4. **Run the application**
```bash
python run_server.py
```

The system will be available at:
- **Live Dashboard**: `http://localhost:8000/dashboard`
- **API Documentation**: `http://localhost:8000/docs`
- **WebSocket**: `ws://localhost:8000/ws/live`

## 🚀 **Enhanced API Endpoints**

### 🤖 **AI Analysis & Predictions**
```http
POST /ai/analyze
```
Get comprehensive AI analysis with buy/sell suggestions and profit predictions.

**Request:**
```json
{
  "symbol": "AAPL",
  "timeframe": "1d",
  "include_notifications": true
}
```

**Response:**
```json
{
  "symbol": "AAPL",
  "analysis": {
    "suggestions": {
      "action": "STRONG_BUY",
      "confidence": 0.85,
      "target_price": 195.50,
      "reasoning": ["AI predicts 8.2% gain with 85% confidence", "Strong upward trend"]
    },
    "profit_potential": {
      "scenarios": [
        {
          "investment": 1000,
          "potential_profit": 82.50,
          "profit_percentage": 8.25,
          "total_value": 1082.50
        }
      ]
    },
    "risk_level": "LOW",
    "market_sentiment": "VERY_BULLISH"
  }
}
```

### 🔍 **Company Search & Live Data**
```http
POST /search/companies
```
Search for any company by name, symbol, or keyword.

```http
GET /company/{symbol}
```
Get comprehensive company details with live trade data.

```http
GET /company/{symbol}/live
```
Get real-time trading data with current prices and statistics.

### Company Validation
```http
GET /validate/{symbol}
```
Validate company legitimacy and assess risk.

### Technical Analysis
```http
GET /technical/{symbol}?period=1y&interval=1d
```
Get comprehensive technical analysis including indicators and patterns.

### Trading Signals
```http
GET /signals/{symbol}?period=1y
```
Generate comprehensive trading signals with buy/sell recommendations.

**Example Response:**
```json
{
  "symbol": "AAPL",
  "primary_signal": "BUY",
  "confidence": 0.78,
  "risk_level": "LOW",
  "entry_price": 150.25,
  "stop_loss": 145.30,
  "take_profit": 158.40,
  "reasoning": ["Strong upward trend", "Bullish technical indicators"]
}
```

### Price Prediction
```http
GET /predict/{symbol}?days_ahead=1&period=2y
```
Predict future prices using AI models.

### Temporal Analysis
```http
GET /temporal/{symbol}?period=1y
```
Analyze yesterday's, today's, and tomorrow's trends.

### Batch Analysis
```http
POST /batch/analyze
```
Analyze multiple symbols simultaneously.

**Request Body:**
```json
{
  "symbols": ["AAPL", "GOOGL", "MSFT"],
  "period": "1y",
  "interval": "1d"
}
```

### Market Overview
```http
GET /market/overview
```
Get overall market sentiment and analysis of major stocks.

## Configuration

Edit `config.py` to customize:

- **API Keys**: Set your financial data API keys
- **Model Parameters**: Adjust AI model settings
- **Technical Indicators**: Configure indicator periods
- **Risk Thresholds**: Set company validation criteria
- **Default Symbols**: Change default stocks for analysis

## Testing

Run the comprehensive test suite:

```bash
# Run all tests
python -m pytest tests/ -v

# Run specific test file
python tests/test_trading_system.py

# Run performance tests
python tests/test_trading_system.py
```

## Architecture

### Core Components

1. **Data Collector** (`utils/data_collector.py`)
   - Fetches historical and real-time market data
   - Supports multiple data sources (Yahoo Finance, Alpha Vantage, Finnhub)
   - Handles data validation and cleaning

2. **Company Validator** (`utils/company_validator.py`)
   - Validates company legitimacy
   - Assesses financial health and compliance
   - Detects potential fraud indicators

3. **Technical Analyzer** (`utils/technical_analysis.py`)
   - Calculates 50+ technical indicators
   - Detects candlestick patterns
   - Performs trend and volatility analysis

4. **AI Predictor** (`models/ai_predictor.py`)
   - Implements multiple ML models
   - Handles feature engineering
   - Provides price and trend predictions

5. **Timeframe Analyzer** (`utils/timeframe_analyzer.py`)
   - Correlates multi-timeframe trends
   - Analyzes temporal patterns
   - Generates temporal insights

6. **Signal Generator** (`utils/signal_generator.py`)
   - Combines all analysis methods
   - Generates final trading recommendations
   - Calculates risk-adjusted position sizes

### Data Flow

```
Market Data → Technical Analysis → AI Prediction → Signal Generation → API Response
     ↓              ↓                    ↓              ↓
Company Validation → Risk Assessment → Temporal Analysis → Final Recommendation
```

## Usage Examples

### Python Client Example

```python
import requests

# Get trading signals
response = requests.get("http://localhost:8000/signals/AAPL")
signals = response.json()

print(f"Signal: {signals['primary_signal']}")
print(f"Confidence: {signals['confidence']:.2%}")
print(f"Entry Price: ${signals['entry_price']:.2f}")
```

### Batch Analysis Example

```python
import requests

# Analyze multiple stocks
payload = {
    "symbols": ["AAPL", "GOOGL", "MSFT", "TSLA"],
    "period": "6mo"
}

response = requests.post("http://localhost:8000/batch/analyze", json=payload)
results = response.json()

for symbol, analysis in results['results'].items():
    print(f"{symbol}: {analysis['primary_signal']} (Confidence: {analysis['confidence']:.2%})")
```

## Performance

- **Data Collection**: ~2-3 seconds per symbol
- **Technical Analysis**: ~0.5 seconds per symbol
- **AI Prediction**: ~3-5 seconds per symbol (first run, cached afterward)
- **Complete Analysis**: ~5-8 seconds per symbol

## Limitations

- **Market Hours**: Some real-time data only available during market hours
- **API Limits**: Rate limits apply to external data sources
- **Prediction Accuracy**: AI predictions are probabilistic, not guaranteed
- **Historical Bias**: Models trained on historical data may not predict unprecedented events

## Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Disclaimer

This software is for educational and research purposes only. It should not be used as the sole basis for investment decisions. Always consult with qualified financial advisors and conduct your own research before making investment decisions. The authors are not responsible for any financial losses incurred from using this software.

## Support

For support, please open an issue on GitHub or contact the development team.

---

**Built with ❤️ for the trading community**
