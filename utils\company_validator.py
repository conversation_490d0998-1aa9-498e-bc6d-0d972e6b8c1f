"""
Company Validation System for verifying legitimacy of trading companies
"""
import yfinance as yf
import requests
import pandas as pd
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta
from loguru import logger
from config import Config
import re

class CompanyValidator:
    def __init__(self):
        self.config = Config()
        self.sec_base_url = "https://www.sec.gov/Archives/edgar/data"
        self.major_exchanges = ['NYSE', 'NASDAQ', 'AMEX']
        
    def validate_company_legitimacy(self, symbol: str) -> Dict:
        """
        Comprehensive validation of company legitimacy
        
        Args:
            symbol: Stock symbol to validate
            
        Returns:
            Dictionary with validation results and scores
        """
        validation_results = {
            'symbol': symbol,
            'is_legitimate': False,
            'legitimacy_score': 0.0,
            'validation_details': {},
            'red_flags': [],
            'validation_timestamp': datetime.now()
        }
        
        try:
            # Get basic company information
            ticker = yf.Ticker(symbol)
            info = ticker.info
            
            if not info:
                validation_results['red_flags'].append("No company information available")
                return validation_results
            
            # Perform various validation checks
            exchange_score = self._validate_exchange(info)
            financial_score = self._validate_financials(info)
            volume_score = self._validate_trading_volume(info)
            history_score = self._validate_trading_history(ticker)
            regulatory_score = self._validate_regulatory_compliance(info)
            
            # Calculate overall legitimacy score
            scores = [exchange_score, financial_score, volume_score, history_score, regulatory_score]
            validation_results['legitimacy_score'] = sum(scores) / len(scores)
            validation_results['is_legitimate'] = validation_results['legitimacy_score'] >= 0.7
            
            # Store detailed validation results
            validation_results['validation_details'] = {
                'exchange_validation': exchange_score,
                'financial_validation': financial_score,
                'volume_validation': volume_score,
                'history_validation': history_score,
                'regulatory_validation': regulatory_score,
                'company_name': info.get('longName', ''),
                'sector': info.get('sector', ''),
                'industry': info.get('industry', ''),
                'market_cap': info.get('marketCap', 0),
                'exchange': info.get('exchange', ''),
                'country': info.get('country', ''),
                'website': info.get('website', '')
            }
            
            logger.info(f"Validation completed for {symbol}: Score {validation_results['legitimacy_score']:.2f}")
            
        except Exception as e:
            logger.error(f"Error validating company {symbol}: {str(e)}")
            validation_results['red_flags'].append(f"Validation error: {str(e)}")
        
        return validation_results
    
    def _validate_exchange(self, info: Dict) -> float:
        """Validate if company is listed on a major exchange"""
        exchange = info.get('exchange', '').upper()
        
        if exchange in self.major_exchanges:
            return 1.0
        elif exchange in ['OTC', 'OTCBB', 'PINK']:
            self._add_red_flag("Listed on OTC market - higher risk")
            return 0.3
        else:
            self._add_red_flag("Unknown or unlisted exchange")
            return 0.0
    
    def _validate_financials(self, info: Dict) -> float:
        """Validate financial metrics"""
        score = 0.0
        total_checks = 5
        
        # Market cap check
        market_cap = info.get('marketCap', 0)
        if market_cap >= self.config.MIN_MARKET_CAP:
            score += 0.2
        elif market_cap > 0:
            score += 0.1
        
        # Revenue check
        revenue = info.get('totalRevenue', 0)
        if revenue > 0:
            score += 0.2
        
        # Profit margins check
        profit_margins = info.get('profitMargins', 0)
        if profit_margins > 0:
            score += 0.2
        elif profit_margins > -0.5:  # Not too negative
            score += 0.1
        
        # Debt to equity check
        debt_to_equity = info.get('debtToEquity', 0)
        if 0 < debt_to_equity < 100:  # Reasonable debt levels
            score += 0.2
        elif debt_to_equity == 0:
            score += 0.1
        
        # Beta check (volatility)
        beta = info.get('beta', 0)
        if 0.5 <= beta <= 2.0:  # Reasonable volatility
            score += 0.2
        elif beta > 0:
            score += 0.1
        
        return score
    
    def _validate_trading_volume(self, info: Dict) -> float:
        """Validate trading volume and liquidity"""
        avg_volume = info.get('averageVolume', 0)
        
        if avg_volume >= self.config.MIN_TRADING_VOLUME:
            return 1.0
        elif avg_volume >= self.config.MIN_TRADING_VOLUME * 0.5:
            return 0.7
        elif avg_volume > 0:
            return 0.3
        else:
            self._add_red_flag("Insufficient trading volume")
            return 0.0
    
    def _validate_trading_history(self, ticker) -> float:
        """Validate trading history and data availability"""
        try:
            # Get 1 year of historical data
            hist = ticker.history(period="1y")
            
            if hist.empty:
                self._add_red_flag("No trading history available")
                return 0.0
            
            # Check data consistency
            days_with_data = len(hist)
            expected_days = 252  # Approximate trading days in a year
            
            if days_with_data >= expected_days * 0.8:
                return 1.0
            elif days_with_data >= expected_days * 0.5:
                return 0.7
            elif days_with_data > 0:
                return 0.3
            else:
                return 0.0
                
        except Exception as e:
            logger.error(f"Error validating trading history: {str(e)}")
            return 0.0
    
    def _validate_regulatory_compliance(self, info: Dict) -> float:
        """Validate regulatory compliance indicators"""
        score = 0.0
        
        # Check if company has basic required information
        required_fields = ['longName', 'sector', 'industry', 'country']
        available_fields = sum(1 for field in required_fields if info.get(field))
        score += (available_fields / len(required_fields)) * 0.5
        
        # Check for website presence
        if info.get('website'):
            score += 0.2
        
        # Check for business summary
        if info.get('longBusinessSummary'):
            score += 0.2
        
        # Check for employee count (indicates real business)
        if info.get('fullTimeEmployees', 0) > 0:
            score += 0.1
        
        return score
    
    def _add_red_flag(self, flag: str):
        """Add a red flag to the current validation"""
        if not hasattr(self, '_current_red_flags'):
            self._current_red_flags = []
        self._current_red_flags.append(flag)
    
    def check_penny_stock(self, symbol: str) -> bool:
        """Check if stock is a penny stock (high risk)"""
        try:
            ticker = yf.Ticker(symbol)
            info = ticker.info
            current_price = info.get('currentPrice', 0)
            
            return current_price < 5.0  # Stocks under $5 are often considered penny stocks
            
        except Exception as e:
            logger.error(f"Error checking penny stock status for {symbol}: {str(e)}")
            return False
    
    def get_insider_trading_info(self, symbol: str) -> Dict:
        """Get insider trading information (basic implementation)"""
        try:
            ticker = yf.Ticker(symbol)
            
            # Get insider transactions
            insider_transactions = ticker.insider_transactions
            insider_roster = ticker.insider_roster_holders
            
            return {
                'has_insider_data': not (insider_transactions.empty and insider_roster.empty),
                'recent_transactions': len(insider_transactions) if not insider_transactions.empty else 0,
                'insider_count': len(insider_roster) if not insider_roster.empty else 0
            }
            
        except Exception as e:
            logger.error(f"Error getting insider info for {symbol}: {str(e)}")
            return {'has_insider_data': False, 'recent_transactions': 0, 'insider_count': 0}
    
    def validate_multiple_companies(self, symbols: List[str]) -> Dict[str, Dict]:
        """Validate multiple companies"""
        results = {}
        
        for symbol in symbols:
            try:
                results[symbol] = self.validate_company_legitimacy(symbol)
            except Exception as e:
                logger.error(f"Error validating {symbol}: {str(e)}")
                results[symbol] = {
                    'symbol': symbol,
                    'is_legitimate': False,
                    'legitimacy_score': 0.0,
                    'red_flags': [f"Validation failed: {str(e)}"]
                }
        
        return results
    
    def get_risk_assessment(self, symbol: str) -> Dict:
        """Get comprehensive risk assessment"""
        validation = self.validate_company_legitimacy(symbol)
        
        risk_level = "LOW"
        if validation['legitimacy_score'] < 0.3:
            risk_level = "VERY HIGH"
        elif validation['legitimacy_score'] < 0.5:
            risk_level = "HIGH"
        elif validation['legitimacy_score'] < 0.7:
            risk_level = "MEDIUM"
        
        return {
            'symbol': symbol,
            'risk_level': risk_level,
            'legitimacy_score': validation['legitimacy_score'],
            'is_penny_stock': self.check_penny_stock(symbol),
            'red_flags': validation['red_flags'],
            'recommendation': self._get_recommendation(validation['legitimacy_score'])
        }
    
    def _get_recommendation(self, score: float) -> str:
        """Get trading recommendation based on legitimacy score"""
        if score >= 0.8:
            return "SAFE TO TRADE"
        elif score >= 0.6:
            return "PROCEED WITH CAUTION"
        elif score >= 0.4:
            return "HIGH RISK - CAREFUL ANALYSIS REQUIRED"
        else:
            return "AVOID - HIGH RISK OF FRAUD"
