"""
Advanced Notification System for Trading Alerts and Profit Notifications
"""
import asyncio
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Callable
from loguru import logger
from dataclasses import dataclass, asdict
from enum import Enum
import smtplib
from email.mime.text import MimeText
from email.mime.multipart import MimeMultipart

class NotificationType(Enum):
    PROFIT_ALERT = "PROFIT_ALERT"
    SIGNAL_CHANGE = "SIGNAL_CHANGE"
    PRICE_TARGET = "PRICE_TARGET"
    RISK_WARNING = "RISK_WARNING"
    MARKET_OPPORTUNITY = "MARKET_OPPORTUNITY"
    PORTFOLIO_UPDATE = "PORTFOLIO_UPDATE"

class NotificationPriority(Enum):
    LOW = "LOW"
    MEDIUM = "MEDIUM"
    HIGH = "HIGH"
    CRITICAL = "CRITICAL"

@dataclass
class Notification:
    id: str
    type: NotificationType
    priority: NotificationPriority
    title: str
    message: str
    symbol: str
    data: Dict
    timestamp: datetime
    expires_at: Optional[datetime] = None
    is_read: bool = False
    is_sent: bool = False
    
    def to_dict(self):
        data = asdict(self)
        data['type'] = self.type.value
        data['priority'] = self.priority.value
        data['timestamp'] = self.timestamp.isoformat()
        if self.expires_at:
            data['expires_at'] = self.expires_at.isoformat()
        return data

class NotificationSystem:
    def __init__(self):
        self.notifications = {}  # {notification_id: Notification}
        self.subscribers = {}  # {user_id: [websocket_connections]}
        self.notification_queue = asyncio.Queue()
        self.is_running = False
        self.notification_counter = 0
        
        # Email settings (optional)
        self.email_enabled = False
        self.smtp_server = None
        self.smtp_port = 587
        self.email_username = None
        self.email_password = None
        
    async def start_notification_service(self):
        """Start the notification service"""
        self.is_running = True
        logger.info("Notification service started")
        
        # Start background tasks
        asyncio.create_task(self._process_notification_queue())
        asyncio.create_task(self._cleanup_expired_notifications())
    
    async def create_profit_alert(self, symbol: str, analysis_data: Dict, user_id: str = "default") -> str:
        """Create profit alert notification"""
        try:
            suggestions = analysis_data.get('suggestions', {})
            profit_potential = analysis_data.get('profit_potential', {})
            
            action = suggestions.get('action', 'HOLD')
            confidence = suggestions.get('confidence', 0)
            current_price = analysis_data.get('current_price', 0)
            
            # Determine priority based on profit potential and confidence
            priority = self._determine_priority(action, confidence, profit_potential)
            
            # Create notification message
            title, message = self._create_profit_message(symbol, action, suggestions, profit_potential)
            
            notification = Notification(
                id=self._generate_notification_id(),
                type=NotificationType.PROFIT_ALERT,
                priority=priority,
                title=title,
                message=message,
                symbol=symbol,
                data={
                    'action': action,
                    'confidence': confidence,
                    'current_price': current_price,
                    'profit_scenarios': profit_potential.get('scenarios', []),
                    'target_price': suggestions.get('target_price', current_price),
                    'reasoning': suggestions.get('reasoning', [])
                },
                timestamp=datetime.now(),
                expires_at=datetime.now() + timedelta(hours=24)
            )
            
            await self._queue_notification(notification, user_id)
            return notification.id
            
        except Exception as e:
            logger.error(f"Error creating profit alert: {str(e)}")
            return ""
    
    async def create_signal_change_alert(self, symbol: str, old_signal: str, new_signal: str, 
                                       confidence: float, user_id: str = "default") -> str:
        """Create signal change notification"""
        try:
            priority = NotificationPriority.HIGH if confidence > 0.7 else NotificationPriority.MEDIUM
            
            title = f"🚨 Signal Change: {symbol}"
            message = f"{symbol} signal changed from {old_signal} to {new_signal} (Confidence: {confidence:.1%})"
            
            notification = Notification(
                id=self._generate_notification_id(),
                type=NotificationType.SIGNAL_CHANGE,
                priority=priority,
                title=title,
                message=message,
                symbol=symbol,
                data={
                    'old_signal': old_signal,
                    'new_signal': new_signal,
                    'confidence': confidence
                },
                timestamp=datetime.now(),
                expires_at=datetime.now() + timedelta(hours=12)
            )
            
            await self._queue_notification(notification, user_id)
            return notification.id
            
        except Exception as e:
            logger.error(f"Error creating signal change alert: {str(e)}")
            return ""
    
    async def create_price_target_alert(self, symbol: str, current_price: float, target_price: float,
                                      target_type: str, user_id: str = "default") -> str:
        """Create price target reached notification"""
        try:
            change_pct = (target_price - current_price) / current_price * 100
            
            title = f"🎯 Price Target: {symbol}"
            message = f"{symbol} reached {target_type} target of ${target_price:.2f} ({change_pct:+.1f}%)"
            
            notification = Notification(
                id=self._generate_notification_id(),
                type=NotificationType.PRICE_TARGET,
                priority=NotificationPriority.HIGH,
                title=title,
                message=message,
                symbol=symbol,
                data={
                    'current_price': current_price,
                    'target_price': target_price,
                    'target_type': target_type,
                    'change_percentage': change_pct
                },
                timestamp=datetime.now(),
                expires_at=datetime.now() + timedelta(hours=6)
            )
            
            await self._queue_notification(notification, user_id)
            return notification.id
            
        except Exception as e:
            logger.error(f"Error creating price target alert: {str(e)}")
            return ""
    
    async def create_market_opportunity_alert(self, opportunities: List[Dict], 
                                            scan_type: str, user_id: str = "default") -> str:
        """Create market opportunity notification"""
        try:
            if not opportunities:
                return ""
            
            top_opportunity = opportunities[0]
            symbol = top_opportunity.get('symbol', 'Unknown')
            score = top_opportunity.get('score', 0)
            
            title = f"🎯 Market Opportunity: {scan_type.title()}"
            message = f"Found {len(opportunities)} {scan_type} opportunities. Top pick: {symbol} (Score: {score})"
            
            notification = Notification(
                id=self._generate_notification_id(),
                type=NotificationType.MARKET_OPPORTUNITY,
                priority=NotificationPriority.MEDIUM,
                title=title,
                message=message,
                symbol=symbol,
                data={
                    'scan_type': scan_type,
                    'opportunities_count': len(opportunities),
                    'top_opportunities': opportunities[:5],
                    'top_symbol': symbol,
                    'top_score': score
                },
                timestamp=datetime.now(),
                expires_at=datetime.now() + timedelta(hours=4)
            )
            
            await self._queue_notification(notification, user_id)
            return notification.id
            
        except Exception as e:
            logger.error(f"Error creating market opportunity alert: {str(e)}")
            return ""
    
    async def create_risk_warning(self, symbol: str, risk_level: str, risk_factors: List[str],
                                user_id: str = "default") -> str:
        """Create risk warning notification"""
        try:
            priority = NotificationPriority.CRITICAL if risk_level == "VERY_HIGH" else NotificationPriority.HIGH
            
            title = f"⚠️ Risk Warning: {symbol}"
            message = f"{symbol} has {risk_level} risk level. Factors: {', '.join(risk_factors[:2])}"
            
            notification = Notification(
                id=self._generate_notification_id(),
                type=NotificationType.RISK_WARNING,
                priority=priority,
                title=title,
                message=message,
                symbol=symbol,
                data={
                    'risk_level': risk_level,
                    'risk_factors': risk_factors
                },
                timestamp=datetime.now(),
                expires_at=datetime.now() + timedelta(hours=8)
            )
            
            await self._queue_notification(notification, user_id)
            return notification.id
            
        except Exception as e:
            logger.error(f"Error creating risk warning: {str(e)}")
            return ""
    
    async def _queue_notification(self, notification: Notification, user_id: str):
        """Queue notification for processing"""
        self.notifications[notification.id] = notification
        await self.notification_queue.put((notification, user_id))
        logger.info(f"Queued {notification.type.value} notification for {notification.symbol}")
    
    async def _process_notification_queue(self):
        """Process notification queue"""
        while self.is_running:
            try:
                notification, user_id = await self.notification_queue.get()
                
                # Send to WebSocket subscribers
                await self._send_websocket_notification(notification, user_id)
                
                # Send email if enabled and high priority
                if (self.email_enabled and 
                    notification.priority in [NotificationPriority.HIGH, NotificationPriority.CRITICAL]):
                    await self._send_email_notification(notification, user_id)
                
                notification.is_sent = True
                
            except Exception as e:
                logger.error(f"Error processing notification: {str(e)}")
    
    async def _send_websocket_notification(self, notification: Notification, user_id: str):
        """Send notification via WebSocket"""
        try:
            if user_id in self.subscribers:
                message = {
                    'type': 'notification',
                    'notification': notification.to_dict()
                }
                
                disconnected = []
                for websocket in self.subscribers[user_id]:
                    try:
                        await websocket.send(json.dumps(message))
                    except Exception:
                        disconnected.append(websocket)
                
                # Remove disconnected websockets
                for ws in disconnected:
                    self.subscribers[user_id].remove(ws)
                    
        except Exception as e:
            logger.error(f"Error sending WebSocket notification: {str(e)}")
    
    async def _send_email_notification(self, notification: Notification, user_id: str):
        """Send notification via email (if configured)"""
        try:
            if not self.email_enabled:
                return
            
            # This is a placeholder - implement actual email sending
            logger.info(f"Would send email notification: {notification.title}")
            
        except Exception as e:
            logger.error(f"Error sending email notification: {str(e)}")
    
    async def _cleanup_expired_notifications(self):
        """Clean up expired notifications"""
        while self.is_running:
            try:
                current_time = datetime.now()
                expired_ids = []
                
                for notification_id, notification in self.notifications.items():
                    if (notification.expires_at and 
                        current_time > notification.expires_at):
                        expired_ids.append(notification_id)
                
                for notification_id in expired_ids:
                    del self.notifications[notification_id]
                
                if expired_ids:
                    logger.info(f"Cleaned up {len(expired_ids)} expired notifications")
                
                await asyncio.sleep(300)  # Check every 5 minutes
                
            except Exception as e:
                logger.error(f"Error cleaning up notifications: {str(e)}")
                await asyncio.sleep(60)
    
    def subscribe_websocket(self, websocket, user_id: str = "default"):
        """Subscribe WebSocket to notifications"""
        if user_id not in self.subscribers:
            self.subscribers[user_id] = []
        
        self.subscribers[user_id].append(websocket)
        logger.info(f"WebSocket subscribed for user {user_id}")
    
    def unsubscribe_websocket(self, websocket, user_id: str = "default"):
        """Unsubscribe WebSocket from notifications"""
        if user_id in self.subscribers and websocket in self.subscribers[user_id]:
            self.subscribers[user_id].remove(websocket)
            logger.info(f"WebSocket unsubscribed for user {user_id}")
    
    def get_notifications(self, user_id: str = "default", limit: int = 50, 
                         unread_only: bool = False) -> List[Dict]:
        """Get notifications for a user"""
        try:
            notifications = list(self.notifications.values())
            
            # Filter by read status if requested
            if unread_only:
                notifications = [n for n in notifications if not n.is_read]
            
            # Sort by timestamp (newest first)
            notifications.sort(key=lambda x: x.timestamp, reverse=True)
            
            # Limit results
            notifications = notifications[:limit]
            
            return [n.to_dict() for n in notifications]
            
        except Exception as e:
            logger.error(f"Error getting notifications: {str(e)}")
            return []
    
    def mark_notification_read(self, notification_id: str) -> bool:
        """Mark notification as read"""
        try:
            if notification_id in self.notifications:
                self.notifications[notification_id].is_read = True
                return True
            return False
            
        except Exception as e:
            logger.error(f"Error marking notification as read: {str(e)}")
            return False
    
    def _generate_notification_id(self) -> str:
        """Generate unique notification ID"""
        self.notification_counter += 1
        return f"notif_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{self.notification_counter:04d}"
    
    def _determine_priority(self, action: str, confidence: float, profit_potential: Dict) -> NotificationPriority:
        """Determine notification priority"""
        try:
            best_return = profit_potential.get('best_case_return', 0)
            
            if action in ['STRONG_BUY', 'STRONG_SELL'] and confidence > 0.8 and abs(best_return) > 5:
                return NotificationPriority.CRITICAL
            elif action in ['BUY', 'SELL'] and confidence > 0.6 and abs(best_return) > 3:
                return NotificationPriority.HIGH
            elif action != 'HOLD' and confidence > 0.5:
                return NotificationPriority.MEDIUM
            else:
                return NotificationPriority.LOW
                
        except Exception:
            return NotificationPriority.LOW
    
    def _create_profit_message(self, symbol: str, action: str, suggestions: Dict, 
                             profit_potential: Dict) -> tuple:
        """Create profit alert message"""
        try:
            confidence = suggestions.get('confidence', 0)
            scenarios = profit_potential.get('scenarios', [])
            
            if action == 'STRONG_BUY':
                title = f"🚀 Strong Buy Alert: {symbol}"
                emoji = "🚀"
            elif action == 'BUY':
                title = f"📈 Buy Alert: {symbol}"
                emoji = "📈"
            elif action == 'STRONG_SELL':
                title = f"📉 Strong Sell Alert: {symbol}"
                emoji = "📉"
            elif action == 'SELL':
                title = f"⬇️ Sell Alert: {symbol}"
                emoji = "⬇️"
            else:
                title = f"📊 Analysis Update: {symbol}"
                emoji = "📊"
            
            # Create profit scenarios text
            profit_text = ""
            if scenarios:
                best_scenario = max(scenarios, key=lambda x: abs(x.get('profit_percentage', 0)))
                profit_text = f"Potential: {best_scenario.get('profit_percentage', 0):+.1f}% "
            
            message = f"{emoji} {symbol} - {action} (Confidence: {confidence:.1%}) {profit_text}"
            
            # Add reasoning
            reasoning = suggestions.get('reasoning', [])
            if reasoning:
                message += f"Reasons: {', '.join(reasoning[:2])}"
            
            return title, message
            
        except Exception as e:
            logger.error(f"Error creating profit message: {str(e)}")
            return f"Alert: {symbol}", f"{symbol} - {action}"
    
    def get_notification_stats(self) -> Dict:
        """Get notification statistics"""
        try:
            total = len(self.notifications)
            unread = sum(1 for n in self.notifications.values() if not n.is_read)
            by_type = {}
            by_priority = {}
            
            for notification in self.notifications.values():
                # Count by type
                type_key = notification.type.value
                by_type[type_key] = by_type.get(type_key, 0) + 1
                
                # Count by priority
                priority_key = notification.priority.value
                by_priority[priority_key] = by_priority.get(priority_key, 0) + 1
            
            return {
                'total_notifications': total,
                'unread_notifications': unread,
                'by_type': by_type,
                'by_priority': by_priority,
                'active_subscribers': sum(len(subs) for subs in self.subscribers.values())
            }
            
        except Exception as e:
            logger.error(f"Error getting notification stats: {str(e)}")
            return {}
    
    def stop_service(self):
        """Stop the notification service"""
        self.is_running = False
        logger.info("Notification service stopped")
