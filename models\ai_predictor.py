"""
AI Prediction Models for stock price and trend prediction
"""
import pandas as pd
import numpy as np
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import LinearRegression
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import xgboost as xgb
import lightgbm as lgb
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import LSTM, Dense, Dropout
from tensorflow.keras.optimizers import Adam
import joblib
from typing import Dict, List, Tuple, Optional
from datetime import datetime, timedelta
from loguru import logger
from config import Config
import warnings
warnings.filterwarnings('ignore')

class AIPredictor:
    def __init__(self):
        self.config = Config()
        self.models = {}
        self.scalers = {}
        self.feature_columns = []
        
    def prepare_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Prepare features for machine learning models
        
        Args:
            df: DataFrame with technical indicators
            
        Returns:
            DataFrame with engineered features
        """
        try:
            df = df.copy()
            
            # Price-based features
            df['Price_Change'] = df['Close'].pct_change()
            df['Price_Change_2d'] = df['Close'].pct_change(periods=2)
            df['Price_Change_5d'] = df['Close'].pct_change(periods=5)
            
            # Volume features
            df['Volume_Change'] = df['Volume'].pct_change()
            df['Volume_Ratio'] = df['Volume'] / df['Volume_SMA'] if 'Volume_SMA' in df.columns else 1
            
            # Volatility features
            df['High_Low_Ratio'] = (df['High'] - df['Low']) / df['Close']
            df['Open_Close_Ratio'] = (df['Close'] - df['Open']) / df['Open']
            
            # Technical indicator ratios
            if 'RSI' in df.columns:
                df['RSI_Normalized'] = (df['RSI'] - 50) / 50
            
            if 'BB_Upper' in df.columns and 'BB_Lower' in df.columns:
                df['BB_Position'] = (df['Close'] - df['BB_Lower']) / (df['BB_Upper'] - df['BB_Lower'])
            
            # Trend features
            if 'SMA_20' in df.columns:
                df['Price_SMA20_Ratio'] = df['Close'] / df['SMA_20']
            if 'SMA_50' in df.columns:
                df['Price_SMA50_Ratio'] = df['Close'] / df['SMA_50']
            
            # Momentum features
            df['Momentum_5d'] = df['Close'] / df['Close'].shift(5) - 1
            df['Momentum_10d'] = df['Close'] / df['Close'].shift(10) - 1
            
            # Lag features
            for lag in [1, 2, 3, 5]:
                df[f'Close_Lag_{lag}'] = df['Close'].shift(lag)
                df[f'Volume_Lag_{lag}'] = df['Volume'].shift(lag)
            
            # Rolling statistics
            for window in [5, 10, 20]:
                df[f'Close_Rolling_Mean_{window}'] = df['Close'].rolling(window=window).mean()
                df[f'Close_Rolling_Std_{window}'] = df['Close'].rolling(window=window).std()
                df[f'Volume_Rolling_Mean_{window}'] = df['Volume'].rolling(window=window).mean()
            
            # Target variables (future prices)
            df['Target_1d'] = df['Close'].shift(-1)  # Next day price
            df['Target_3d'] = df['Close'].shift(-3)  # 3 days ahead
            df['Target_5d'] = df['Close'].shift(-5)  # 5 days ahead
            
            # Target direction (up/down)
            df['Target_Direction_1d'] = (df['Target_1d'] > df['Close']).astype(int)
            df['Target_Direction_3d'] = (df['Target_3d'] > df['Close']).astype(int)
            
            logger.info(f"Prepared features: {df.shape[1]} columns, {df.shape[0]} rows")
            return df
            
        except Exception as e:
            logger.error(f"Error preparing features: {str(e)}")
            return df
    
    def train_regression_models(self, df: pd.DataFrame, target_col: str = 'Target_1d') -> Dict:
        """
        Train multiple regression models for price prediction
        
        Args:
            df: DataFrame with features and targets
            target_col: Target column name
            
        Returns:
            Dictionary with model performance metrics
        """
        try:
            # Select feature columns (exclude target and non-numeric columns)
            feature_cols = [col for col in df.columns if col not in 
                          ['Target_1d', 'Target_3d', 'Target_5d', 'Target_Direction_1d', 
                           'Target_Direction_3d', 'Date', 'Symbol'] and 
                          df[col].dtype in ['float64', 'int64']]
            
            # Remove rows with NaN values
            clean_df = df[feature_cols + [target_col]].dropna()
            
            if len(clean_df) < 50:
                logger.warning("Insufficient data for training")
                return {}
            
            X = clean_df[feature_cols]
            y = clean_df[target_col]
            
            # Split data
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=0.2, random_state=42, shuffle=False
            )
            
            # Scale features
            scaler = StandardScaler()
            X_train_scaled = scaler.fit_transform(X_train)
            X_test_scaled = scaler.transform(X_test)
            
            self.scalers[target_col] = scaler
            self.feature_columns = feature_cols
            
            # Train multiple models
            models = {
                'RandomForest': RandomForestRegressor(n_estimators=100, random_state=42),
                'GradientBoosting': GradientBoostingRegressor(random_state=42),
                'XGBoost': xgb.XGBRegressor(random_state=42),
                'LightGBM': lgb.LGBMRegressor(random_state=42, verbose=-1)
            }
            
            results = {}
            
            for name, model in models.items():
                try:
                    # Train model
                    if name in ['RandomForest', 'GradientBoosting']:
                        model.fit(X_train, y_train)
                        y_pred = model.predict(X_test)
                    else:
                        model.fit(X_train_scaled, y_train)
                        y_pred = model.predict(X_test_scaled)
                    
                    # Calculate metrics
                    mse = mean_squared_error(y_test, y_pred)
                    mae = mean_absolute_error(y_test, y_pred)
                    r2 = r2_score(y_test, y_pred)
                    
                    results[name] = {
                        'model': model,
                        'mse': mse,
                        'mae': mae,
                        'r2': r2,
                        'rmse': np.sqrt(mse)
                    }
                    
                    logger.info(f"{name} - MSE: {mse:.4f}, MAE: {mae:.4f}, R2: {r2:.4f}")
                    
                except Exception as e:
                    logger.error(f"Error training {name}: {str(e)}")
                    continue
            
            # Select best model based on R2 score
            if results:
                best_model_name = max(results.keys(), key=lambda k: results[k]['r2'])
                self.models[target_col] = results[best_model_name]['model']
                logger.info(f"Best model for {target_col}: {best_model_name}")
            
            return results
            
        except Exception as e:
            logger.error(f"Error training regression models: {str(e)}")
            return {}
    
    def train_lstm_model(self, df: pd.DataFrame, sequence_length: int = 60) -> Dict:
        """
        Train LSTM model for time series prediction
        
        Args:
            df: DataFrame with price data
            sequence_length: Length of input sequences
            
        Returns:
            Dictionary with model performance
        """
        try:
            # Prepare data for LSTM
            prices = df['Close'].values.reshape(-1, 1)
            
            # Scale data
            scaler = MinMaxScaler()
            scaled_prices = scaler.fit_transform(prices)
            
            # Create sequences
            X, y = [], []
            for i in range(sequence_length, len(scaled_prices)):
                X.append(scaled_prices[i-sequence_length:i, 0])
                y.append(scaled_prices[i, 0])
            
            X, y = np.array(X), np.array(y)
            X = X.reshape((X.shape[0], X.shape[1], 1))
            
            # Split data
            split_idx = int(0.8 * len(X))
            X_train, X_test = X[:split_idx], X[split_idx:]
            y_train, y_test = y[:split_idx], y[split_idx:]
            
            # Build LSTM model
            model = Sequential([
                LSTM(50, return_sequences=True, input_shape=(sequence_length, 1)),
                Dropout(0.2),
                LSTM(50, return_sequences=False),
                Dropout(0.2),
                Dense(25),
                Dense(1)
            ])
            
            model.compile(optimizer=Adam(learning_rate=0.001), loss='mse')
            
            # Train model
            history = model.fit(
                X_train, y_train,
                batch_size=32,
                epochs=50,
                validation_data=(X_test, y_test),
                verbose=0
            )
            
            # Make predictions
            y_pred = model.predict(X_test)
            
            # Inverse transform predictions
            y_test_actual = scaler.inverse_transform(y_test.reshape(-1, 1))
            y_pred_actual = scaler.inverse_transform(y_pred)
            
            # Calculate metrics
            mse = mean_squared_error(y_test_actual, y_pred_actual)
            mae = mean_absolute_error(y_test_actual, y_pred_actual)
            
            # Store model and scaler
            self.models['LSTM'] = model
            self.scalers['LSTM'] = scaler
            
            return {
                'model': model,
                'scaler': scaler,
                'mse': mse,
                'mae': mae,
                'rmse': np.sqrt(mse),
                'history': history.history
            }
            
        except Exception as e:
            logger.error(f"Error training LSTM model: {str(e)}")
            return {}
    
    def predict_price(self, df: pd.DataFrame, days_ahead: int = 1) -> Dict:
        """
        Predict future prices using trained models
        
        Args:
            df: DataFrame with current data
            days_ahead: Number of days to predict ahead
            
        Returns:
            Dictionary with predictions from different models
        """
        try:
            predictions = {}
            
            # Prepare features
            df_features = self.prepare_features(df)
            
            target_col = f'Target_{days_ahead}d' if days_ahead <= 5 else 'Target_1d'
            
            if target_col in self.models and self.feature_columns:
                # Get latest features
                latest_features = df_features[self.feature_columns].iloc[-1:].fillna(0)
                
                # Scale features if scaler exists
                if target_col in self.scalers:
                    latest_features_scaled = self.scalers[target_col].transform(latest_features)
                    prediction = self.models[target_col].predict(latest_features_scaled)[0]
                else:
                    prediction = self.models[target_col].predict(latest_features)[0]
                
                predictions['ML_Model'] = prediction
            
            # LSTM prediction
            if 'LSTM' in self.models and 'LSTM' in self.scalers:
                sequence_length = 60
                if len(df) >= sequence_length:
                    recent_prices = df['Close'].tail(sequence_length).values.reshape(-1, 1)
                    scaled_prices = self.scalers['LSTM'].transform(recent_prices)
                    lstm_input = scaled_prices.reshape(1, sequence_length, 1)
                    
                    lstm_pred_scaled = self.models['LSTM'].predict(lstm_input)
                    lstm_pred = self.scalers['LSTM'].inverse_transform(lstm_pred_scaled)[0][0]
                    
                    predictions['LSTM'] = lstm_pred
            
            # Ensemble prediction (average of available models)
            if predictions:
                predictions['Ensemble'] = np.mean(list(predictions.values()))
            
            return predictions
            
        except Exception as e:
            logger.error(f"Error making predictions: {str(e)}")
            return {}
    
    def predict_trend_direction(self, df: pd.DataFrame) -> Dict:
        """
        Predict trend direction (up/down) using classification
        
        Args:
            df: DataFrame with features
            
        Returns:
            Dictionary with trend predictions
        """
        try:
            from sklearn.ensemble import RandomForestClassifier
            from sklearn.metrics import accuracy_score, classification_report
            
            # Prepare features
            df_features = self.prepare_features(df)
            
            # Select features and target
            feature_cols = [col for col in df_features.columns if col not in 
                          ['Target_1d', 'Target_3d', 'Target_5d', 'Target_Direction_1d', 
                           'Target_Direction_3d', 'Date', 'Symbol'] and 
                          df_features[col].dtype in ['float64', 'int64']]
            
            clean_df = df_features[feature_cols + ['Target_Direction_1d']].dropna()
            
            if len(clean_df) < 50:
                return {'prediction': 'UNKNOWN', 'confidence': 0}
            
            X = clean_df[feature_cols]
            y = clean_df['Target_Direction_1d']
            
            # Train classifier
            clf = RandomForestClassifier(n_estimators=100, random_state=42)
            clf.fit(X[:-10], y[:-10])  # Leave last 10 for testing
            
            # Predict latest direction
            latest_features = X.iloc[-1:].fillna(0)
            direction_prob = clf.predict_proba(latest_features)[0]
            direction_pred = clf.predict(latest_features)[0]
            
            return {
                'prediction': 'UP' if direction_pred == 1 else 'DOWN',
                'confidence': max(direction_prob),
                'up_probability': direction_prob[1] if len(direction_prob) > 1 else 0,
                'down_probability': direction_prob[0]
            }
            
        except Exception as e:
            logger.error(f"Error predicting trend direction: {str(e)}")
            return {'prediction': 'UNKNOWN', 'confidence': 0}
    
    def save_models(self, filepath: str):
        """Save trained models to disk"""
        try:
            model_data = {
                'models': self.models,
                'scalers': self.scalers,
                'feature_columns': self.feature_columns
            }
            joblib.dump(model_data, filepath)
            logger.info(f"Models saved to {filepath}")
        except Exception as e:
            logger.error(f"Error saving models: {str(e)}")
    
    def load_models(self, filepath: str):
        """Load trained models from disk"""
        try:
            model_data = joblib.load(filepath)
            self.models = model_data['models']
            self.scalers = model_data['scalers']
            self.feature_columns = model_data['feature_columns']
            logger.info(f"Models loaded from {filepath}")
        except Exception as e:
            logger.error(f"Error loading models: {str(e)}")
