<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Trading Dashboard - Preview</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
            color: #fff;
            min-height: 100vh;
            padding: 20px;
        }
        
        .preview-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .preview-header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .preview-header h1 {
            font-size: 3rem;
            background: linear-gradient(45deg, #00ff88, #00d4ff, #ff6b6b);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 10px;
        }
        
        .preview-section {
            margin-bottom: 50px;
        }
        
        .preview-section h2 {
            color: #ffd700;
            margin-bottom: 20px;
            font-size: 1.8rem;
        }
        
        .mockup {
            background: rgba(255,255,255,0.08);
            border-radius: 20px;
            padding: 25px;
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255,255,255,0.15);
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
            margin-bottom: 30px;
        }
        
        .dashboard-mockup {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .panel {
            background: rgba(255,255,255,0.05);
            border-radius: 15px;
            padding: 20px;
            border: 1px solid rgba(255,255,255,0.1);
        }
        
        .panel h3 {
            color: #ffd700;
            margin-bottom: 15px;
            border-bottom: 2px solid #ffd700;
            padding-bottom: 8px;
        }
        
        .signal-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px;
            margin-bottom: 10px;
            background: rgba(255,255,255,0.05);
            border-radius: 8px;
            border-left: 4px solid;
        }
        
        .signal-buy { border-left-color: #00ff88; }
        .signal-sell { border-left-color: #ff4444; }
        .signal-hold { border-left-color: #ffaa00; }
        
        .signal-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: bold;
        }
        
        .buy { background: #00ff88; color: #000; }
        .sell { background: #ff4444; color: #fff; }
        .hold { background: #ffaa00; color: #000; }
        
        .profit-scenario {
            background: rgba(0,255,136,0.1);
            padding: 15px;
            border-radius: 10px;
            margin: 10px 0;
            border-left: 3px solid #00ff88;
        }
        
        .profit-amount {
            color: #00ff88;
            font-size: 1.2rem;
            font-weight: bold;
        }
        
        .notification-item {
            padding: 12px;
            margin-bottom: 8px;
            background: rgba(255,255,255,0.05);
            border-radius: 8px;
            border-left: 4px solid #ffd700;
        }
        
        .mobile-mockup {
            max-width: 350px;
            margin: 0 auto;
            background: rgba(255,255,255,0.08);
            border-radius: 25px;
            padding: 20px;
            border: 2px solid rgba(255,255,255,0.2);
        }
        
        .status-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        
        .status-item {
            text-align: center;
        }
        
        .status-value {
            font-size: 1.2rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .status-label {
            font-size: 0.8rem;
            opacity: 0.8;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        
        .feature-card {
            background: rgba(255,255,255,0.05);
            border-radius: 15px;
            padding: 20px;
            border: 1px solid rgba(255,255,255,0.1);
            text-align: center;
        }
        
        .feature-icon {
            font-size: 3rem;
            margin-bottom: 15px;
        }
        
        @media (max-width: 768px) {
            .dashboard-mockup {
                grid-template-columns: 1fr;
            }
            
            .preview-header h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="preview-container">
        <div class="preview-header">
            <h1>🚀 AI Trading Dashboard</h1>
            <p>Advanced AI-Powered Trading Analysis with Real-Time Profit Predictions</p>
        </div>
        
        <div class="preview-section">
            <h2>📊 Main Dashboard View</h2>
            <div class="mockup">
                <div class="status-bar">
                    <div class="status-item">
                        <div class="status-value" style="color: #00ff88;">CONNECTED</div>
                        <div class="status-label">Connection</div>
                    </div>
                    <div class="status-item">
                        <div class="status-value">OPEN</div>
                        <div class="status-label">Market Status</div>
                    </div>
                    <div class="status-item">
                        <div class="status-value">6</div>
                        <div class="status-label">Monitored Symbols</div>
                    </div>
                    <div class="status-item">
                        <div class="status-value">4</div>
                        <div class="status-label">Active Signals</div>
                    </div>
                    <div class="status-item">
                        <div class="status-value">14:32</div>
                        <div class="status-label">Last Update</div>
                    </div>
                </div>
                
                <div class="dashboard-mockup">
                    <div class="panel">
                        <h3>📊 Live Signals</h3>
                        <div class="signal-item signal-buy">
                            <div>
                                <div style="font-weight: bold; font-size: 1.1rem;">AAPL</div>
                                <div style="font-size: 0.9rem; opacity: 0.8;">$189.25</div>
                            </div>
                            <div class="signal-badge buy">STRONG BUY</div>
                        </div>
                        <div class="signal-item signal-buy">
                            <div>
                                <div style="font-weight: bold; font-size: 1.1rem;">GOOGL</div>
                                <div style="font-size: 0.9rem; opacity: 0.8;">$2,847.50</div>
                            </div>
                            <div class="signal-badge buy">BUY</div>
                        </div>
                        <div class="signal-item signal-sell">
                            <div>
                                <div style="font-weight: bold; font-size: 1.1rem;">TSLA</div>
                                <div style="font-size: 0.9rem; opacity: 0.8;">$248.75</div>
                            </div>
                            <div class="signal-badge sell">SELL</div>
                        </div>
                        <div class="signal-item signal-buy">
                            <div>
                                <div style="font-weight: bold; font-size: 1.1rem;">MSFT</div>
                                <div style="font-size: 0.9rem; opacity: 0.8;">$378.90</div>
                            </div>
                            <div class="signal-badge buy">STRONG BUY</div>
                        </div>
                    </div>
                    
                    <div class="panel">
                        <h3>🔔 Notifications</h3>
                        <div class="notification-item">
                            <div style="font-weight: bold; color: #00ff88;">🚀 Strong Buy Alert: AAPL</div>
                            <div style="font-size: 0.9rem; margin-top: 5px;">AI predicts 8.2% gain with 85% confidence</div>
                        </div>
                        <div class="notification-item">
                            <div style="font-weight: bold; color: #ffd700;">🎯 Price Target: NVDA</div>
                            <div style="font-size: 0.9rem; margin-top: 5px;">NVDA reached target of $875 (+5.2%)</div>
                        </div>
                        <div class="notification-item">
                            <div style="font-weight: bold; color: #00d4ff;">📈 Market Opportunity</div>
                            <div style="font-size: 0.9rem; margin-top: 5px;">Found 15 breakout opportunities</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="preview-section">
            <h2>🤖 AI Analysis with Profit Predictions</h2>
            <div class="mockup">
                <div class="panel">
                    <h3>🤖 AI Analysis Results</h3>
                    <div style="background: rgba(255,255,255,0.05); padding: 20px; border-radius: 10px;">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                            <div>
                                <div style="font-size: 1.3rem; font-weight: bold;">AAPL</div>
                                <div style="opacity: 0.8;">AI Recommendation</div>
                            </div>
                            <div class="signal-badge buy">STRONG BUY</div>
                        </div>
                        
                        <div style="margin: 15px 0;">
                            <strong>Confidence:</strong> 85%<br>
                            <strong>Current Price:</strong> $189.25<br>
                            <strong>Target Price:</strong> $205.50<br>
                            <strong>Risk Level:</strong> LOW
                        </div>
                        
                        <div style="margin: 20px 0;">
                            <strong>💰 Profit Potential:</strong>
                        </div>
                        
                        <div class="profit-scenario">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div>
                                    <strong>$1,000 Investment</strong><br>
                                    <span class="profit-amount">+$82.50 (8.2%)</span>
                                </div>
                                <div style="text-align: right;">
                                    <div style="font-size: 0.9rem; opacity: 0.8;">Total Value</div>
                                    <div style="font-size: 1.2rem; font-weight: bold; color: #ffd700;">$1,082.50</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="profit-scenario">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div>
                                    <strong>$5,000 Investment</strong><br>
                                    <span class="profit-amount">+$412.50 (8.2%)</span>
                                </div>
                                <div style="text-align: right;">
                                    <div style="font-size: 0.9rem; opacity: 0.8;">Total Value</div>
                                    <div style="font-size: 1.2rem; font-weight: bold; color: #ffd700;">$5,412.50</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="profit-scenario">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div>
                                    <strong>$10,000 Investment</strong><br>
                                    <span class="profit-amount">+$825.00 (8.2%)</span>
                                </div>
                                <div style="text-align: right;">
                                    <div style="font-size: 0.9rem; opacity: 0.8;">Total Value</div>
                                    <div style="font-size: 1.2rem; font-weight: bold; color: #ffd700;">$10,825.00</div>
                                </div>
                            </div>
                        </div>
                        
                        <div style="margin-top: 15px; padding: 10px; background: rgba(0,255,136,0.05); border-radius: 5px;">
                            <strong>Reasoning:</strong> AI predicts 8.2% gain with 85% confidence, Strong upward trend, Bullish momentum detected
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="preview-section">
            <h2>📱 Mobile View</h2>
            <div class="mobile-mockup">
                <div style="text-align: center; margin-bottom: 20px;">
                    <h3 style="color: #ffd700;">🤖 AI TRADING</h3>
                    <p>DASHBOARD</p>
                </div>
                
                <div style="display: flex; justify-content: space-between; margin-bottom: 15px; font-size: 0.9rem;">
                    <span style="color: #00ff88;">🔴 Connected</span>
                    <span>📊 OPEN</span>
                    <span>👁️ 6</span>
                    <span>📈 4</span>
                </div>
                
                <div style="display: flex; gap: 5px; margin-bottom: 15px;">
                    <button style="background: #00ff88; color: #000; border: none; padding: 8px 12px; border-radius: 5px; font-size: 0.8rem;">Start</button>
                    <button style="background: #ffd700; color: #000; border: none; padding: 8px 12px; border-radius: 5px; font-size: 0.8rem;">Scan</button>
                    <button style="background: #ff4444; color: #fff; border: none; padding: 8px 12px; border-radius: 5px; font-size: 0.8rem;">Stop</button>
                </div>
                
                <div class="signal-item signal-buy" style="margin-bottom: 8px;">
                    <div>
                        <div style="font-weight: bold;">AAPL</div>
                        <div style="font-size: 0.8rem;">$189.25</div>
                    </div>
                    <div class="signal-badge buy" style="font-size: 0.7rem;">STRONG BUY</div>
                </div>
                
                <div class="signal-item signal-buy" style="margin-bottom: 15px;">
                    <div>
                        <div style="font-weight: bold;">GOOGL</div>
                        <div style="font-size: 0.8rem;">$2,847.50</div>
                    </div>
                    <div class="signal-badge buy" style="font-size: 0.7rem;">BUY</div>
                </div>
                
                <div style="background: rgba(255,255,255,0.05); padding: 15px; border-radius: 10px;">
                    <div style="font-weight: bold; margin-bottom: 10px;">🤖 AI Analysis</div>
                    <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                        <span style="font-weight: bold;">AAPL</span>
                        <span class="signal-badge buy" style="font-size: 0.7rem;">STRONG BUY</span>
                    </div>
                    <div style="font-size: 0.9rem; margin-bottom: 8px;">
                        Confidence: 85%<br>
                        Target: $205.50
                    </div>
                    <div style="background: rgba(0,255,136,0.1); padding: 8px; border-radius: 5px; font-size: 0.8rem;">
                        💰 $1K → +$82.50 (8.2% profit)
                    </div>
                </div>
            </div>
        </div>
        
        <div class="preview-section">
            <h2>✨ Key Features</h2>
            <div class="feature-grid">
                <div class="feature-card">
                    <div class="feature-icon">🤖</div>
                    <h3>AI-Powered Analysis</h3>
                    <p>Advanced machine learning models provide buy/sell suggestions with confidence scoring</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">💰</div>
                    <h3>Profit Predictions</h3>
                    <p>See potential profits for different investment amounts with detailed scenarios</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">🔔</div>
                    <h3>Smart Notifications</h3>
                    <p>Real-time alerts for profit opportunities, signal changes, and price targets</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">🔍</div>
                    <h3>Company Search</h3>
                    <p>Find any company with live trade data and comprehensive analysis</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">📊</div>
                    <h3>Live Market Data</h3>
                    <p>Real-time prices, volume, and technical indicators with WebSocket streaming</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">📱</div>
                    <h3>Responsive Design</h3>
                    <p>Works perfectly on desktop, tablet, and mobile devices</p>
                </div>
            </div>
        </div>
        
        <div style="text-align: center; margin-top: 50px; padding: 30px; background: rgba(255,255,255,0.05); border-radius: 20px;">
            <h2 style="color: #ffd700; margin-bottom: 15px;">🚀 Ready to Start Trading?</h2>
            <p style="margin-bottom: 20px;">Experience the power of AI-driven trading analysis</p>
            <div style="display: flex; gap: 15px; justify-content: center; flex-wrap: wrap;">
                <button style="background: linear-gradient(45deg, #00ff88, #00cc6a); color: #000; border: none; padding: 15px 30px; border-radius: 10px; font-weight: bold; cursor: pointer;">
                    Launch Dashboard
                </button>
                <button style="background: linear-gradient(45deg, #ffd700, #ffb700); color: #000; border: none; padding: 15px 30px; border-radius: 10px; font-weight: bold; cursor: pointer;">
                    View API Docs
                </button>
            </div>
        </div>
    </div>
</body>
</html>
