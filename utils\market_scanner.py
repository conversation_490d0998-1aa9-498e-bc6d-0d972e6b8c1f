"""
Real-time Market Scanner for finding trading opportunities
"""
import asyncio
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from loguru import logger
import yfinance as yf
from concurrent.futures import ThreadPoolExecutor
import requests

from config import Config
from utils.technical_analysis import TechnicalAnalyzer
from utils.signal_generator import SignalGenerator

class MarketScanner:
    def __init__(self):
        self.config = Config()
        self.technical_analyzer = TechnicalAnalyzer()
        self.signal_generator = SignalGenerator()
        
        # Popular stock lists
        self.sp500_symbols = self.get_sp500_symbols()
        self.nasdaq100_symbols = self.get_nasdaq100_symbols()
        self.dow_symbols = self.get_dow_symbols()
        
        # Scanning results
        self.scan_results = {}
        self.last_scan_time = None
        
    def get_sp500_symbols(self) -> List[str]:
        """Get S&P 500 symbols"""
        try:
            # Get S&P 500 list from Wikipedia
            url = "https://en.wikipedia.org/wiki/List_of_S%26P_500_companies"
            tables = pd.read_html(url)
            sp500_table = tables[0]
            return sp500_table['Symbol'].tolist()[:100]  # Limit to first 100 for performance
        except Exception as e:
            logger.warning(f"Could not fetch S&P 500 symbols: {e}")
            return self.config.DEFAULT_SYMBOLS
    
    def get_nasdaq100_symbols(self) -> List[str]:
        """Get NASDAQ 100 symbols"""
        # Popular NASDAQ stocks
        return [
            'AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA', 'META', 'NVDA', 'NFLX',
            'ADBE', 'CRM', 'PYPL', 'INTC', 'CMCSA', 'PEP', 'AVGO', 'TXN',
            'QCOM', 'COST', 'SBUX', 'GILD', 'MDLZ', 'ISRG', 'BKNG', 'ADP'
        ]
    
    def get_dow_symbols(self) -> List[str]:
        """Get Dow Jones symbols"""
        return [
            'AAPL', 'MSFT', 'UNH', 'GS', 'HD', 'CAT', 'AMGN', 'MCD',
            'CRM', 'V', 'BA', 'HON', 'IBM', 'AXP', 'JPM', 'JNJ',
            'PG', 'CVX', 'MRK', 'DIS', 'KO', 'NKE', 'MMM', 'DOW',
            'WBA', 'CSCO', 'VZ', 'INTC', 'WMT', 'TRV'
        ]
    
    async def scan_for_opportunities(self, scan_type: str = "breakout") -> Dict:
        """
        Scan market for trading opportunities
        
        Args:
            scan_type: Type of scan ('breakout', 'oversold', 'momentum', 'gap', 'volume')
        """
        logger.info(f"Starting {scan_type} scan")
        
        # Select symbols based on scan type
        if scan_type in ['breakout', 'momentum']:
            symbols = self.nasdaq100_symbols
        elif scan_type == 'oversold':
            symbols = self.sp500_symbols[:50]  # Limit for performance
        else:
            symbols = self.config.DEFAULT_SYMBOLS + self.nasdaq100_symbols[:20]
        
        # Perform scan
        scan_results = await self.perform_scan(symbols, scan_type)
        
        # Store results
        self.scan_results[scan_type] = {
            'timestamp': datetime.now(),
            'results': scan_results,
            'symbols_scanned': len(symbols)
        }
        
        self.last_scan_time = datetime.now()
        
        logger.info(f"Scan completed: {len(scan_results)} opportunities found")
        return self.scan_results[scan_type]
    
    async def perform_scan(self, symbols: List[str], scan_type: str) -> List[Dict]:
        """Perform the actual scanning"""
        opportunities = []
        
        # Use ThreadPoolExecutor for parallel processing
        with ThreadPoolExecutor(max_workers=10) as executor:
            # Create tasks for each symbol
            tasks = []
            for symbol in symbols:
                task = asyncio.get_event_loop().run_in_executor(
                    executor, self.analyze_symbol, symbol, scan_type
                )
                tasks.append(task)
            
            # Wait for all tasks to complete
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Filter successful results
            for result in results:
                if isinstance(result, dict) and result.get('opportunity'):
                    opportunities.append(result)
        
        # Sort by score/priority
        opportunities.sort(key=lambda x: x.get('score', 0), reverse=True)
        
        return opportunities[:20]  # Return top 20 opportunities
    
    def analyze_symbol(self, symbol: str, scan_type: str) -> Dict:
        """Analyze individual symbol for opportunities"""
        try:
            # Get data
            ticker = yf.Ticker(symbol)
            
            # Get different timeframes based on scan type
            if scan_type in ['gap', 'volume']:
                df = ticker.history(period="5d", interval="1d")
                intraday_df = ticker.history(period="1d", interval="5m")
            else:
                df = ticker.history(period="3mo", interval="1d")
                intraday_df = ticker.history(period="2d", interval="15m")
            
            if df.empty:
                return {'symbol': symbol, 'opportunity': False}
            
            # Calculate technical indicators
            df_with_indicators = self.technical_analyzer.calculate_indicators(df)
            
            # Perform specific scan
            if scan_type == "breakout":
                return self.scan_breakout(symbol, df_with_indicators, intraday_df)
            elif scan_type == "oversold":
                return self.scan_oversold(symbol, df_with_indicators)
            elif scan_type == "momentum":
                return self.scan_momentum(symbol, df_with_indicators)
            elif scan_type == "gap":
                return self.scan_gap(symbol, df, intraday_df)
            elif scan_type == "volume":
                return self.scan_volume(symbol, df, intraday_df)
            else:
                return {'symbol': symbol, 'opportunity': False}
                
        except Exception as e:
            logger.error(f"Error analyzing {symbol}: {str(e)}")
            return {'symbol': symbol, 'opportunity': False, 'error': str(e)}
    
    def scan_breakout(self, symbol: str, df: pd.DataFrame, intraday_df: pd.DataFrame) -> Dict:
        """Scan for breakout opportunities"""
        try:
            latest = df.iloc[-1]
            
            # Check for breakout conditions
            breakout_score = 0
            reasons = []
            
            # Price above 20-day high
            if latest['Close'] >= df['High'].rolling(20).max().iloc[-1]:
                breakout_score += 30
                reasons.append("20-day high breakout")
            
            # Volume surge
            avg_volume = df['Volume'].rolling(20).mean().iloc[-1]
            if latest['Volume'] > avg_volume * 1.5:
                breakout_score += 25
                reasons.append("Volume surge")
            
            # RSI momentum
            if 50 < latest['RSI'] < 80:
                breakout_score += 20
                reasons.append("Strong RSI momentum")
            
            # MACD bullish
            if latest['MACD'] > latest['MACD_Signal']:
                breakout_score += 15
                reasons.append("MACD bullish crossover")
            
            # Price above moving averages
            if latest['Close'] > latest['SMA_20'] > latest['SMA_50']:
                breakout_score += 10
                reasons.append("Above key moving averages")
            
            opportunity = breakout_score >= 50
            
            return {
                'symbol': symbol,
                'opportunity': opportunity,
                'scan_type': 'breakout',
                'score': breakout_score,
                'current_price': float(latest['Close']),
                'reasons': reasons,
                'volume_ratio': float(latest['Volume'] / avg_volume),
                'rsi': float(latest['RSI']),
                'timestamp': datetime.now()
            }
            
        except Exception as e:
            return {'symbol': symbol, 'opportunity': False, 'error': str(e)}
    
    def scan_oversold(self, symbol: str, df: pd.DataFrame) -> Dict:
        """Scan for oversold bounce opportunities"""
        try:
            latest = df.iloc[-1]
            
            oversold_score = 0
            reasons = []
            
            # RSI oversold
            if latest['RSI'] < 30:
                oversold_score += 40
                reasons.append(f"RSI oversold ({latest['RSI']:.1f})")
            elif latest['RSI'] < 35:
                oversold_score += 25
                reasons.append(f"RSI near oversold ({latest['RSI']:.1f})")
            
            # Price near Bollinger Band lower
            bb_position = (latest['Close'] - latest['BB_Lower']) / (latest['BB_Upper'] - latest['BB_Lower'])
            if bb_position < 0.1:
                oversold_score += 30
                reasons.append("Near Bollinger Band lower")
            
            # Williams %R oversold
            if latest['Williams_R'] < -80:
                oversold_score += 20
                reasons.append("Williams %R oversold")
            
            # Recent decline
            price_change_5d = (latest['Close'] - df['Close'].iloc[-6]) / df['Close'].iloc[-6] * 100
            if price_change_5d < -5:
                oversold_score += 15
                reasons.append(f"5-day decline ({price_change_5d:.1f}%)")
            
            # Support level test
            support_level = df['Low'].rolling(20).min().iloc[-1]
            if abs(latest['Close'] - support_level) / support_level < 0.02:
                oversold_score += 10
                reasons.append("Testing support level")
            
            opportunity = oversold_score >= 50
            
            return {
                'symbol': symbol,
                'opportunity': opportunity,
                'scan_type': 'oversold',
                'score': oversold_score,
                'current_price': float(latest['Close']),
                'reasons': reasons,
                'rsi': float(latest['RSI']),
                'williams_r': float(latest['Williams_R']),
                'price_change_5d': price_change_5d,
                'timestamp': datetime.now()
            }
            
        except Exception as e:
            return {'symbol': symbol, 'opportunity': False, 'error': str(e)}
    
    def scan_momentum(self, symbol: str, df: pd.DataFrame) -> Dict:
        """Scan for momentum opportunities"""
        try:
            latest = df.iloc[-1]
            
            momentum_score = 0
            reasons = []
            
            # Price momentum
            price_change_1d = (latest['Close'] - df['Close'].iloc[-2]) / df['Close'].iloc[-2] * 100
            price_change_5d = (latest['Close'] - df['Close'].iloc[-6]) / df['Close'].iloc[-6] * 100
            price_change_20d = (latest['Close'] - df['Close'].iloc[-21]) / df['Close'].iloc[-21] * 100
            
            if price_change_1d > 2:
                momentum_score += 25
                reasons.append(f"Strong daily gain ({price_change_1d:.1f}%)")
            
            if price_change_5d > 5:
                momentum_score += 20
                reasons.append(f"5-day momentum ({price_change_5d:.1f}%)")
            
            if price_change_20d > 10:
                momentum_score += 15
                reasons.append(f"20-day momentum ({price_change_20d:.1f}%)")
            
            # RSI momentum
            if 60 < latest['RSI'] < 80:
                momentum_score += 20
                reasons.append("Strong RSI momentum")
            
            # Volume confirmation
            avg_volume = df['Volume'].rolling(10).mean().iloc[-1]
            if latest['Volume'] > avg_volume * 1.2:
                momentum_score += 15
                reasons.append("Volume confirmation")
            
            # Moving average alignment
            if latest['SMA_20'] > latest['SMA_50']:
                momentum_score += 10
                reasons.append("Bullish MA alignment")
            
            opportunity = momentum_score >= 50
            
            return {
                'symbol': symbol,
                'opportunity': opportunity,
                'scan_type': 'momentum',
                'score': momentum_score,
                'current_price': float(latest['Close']),
                'reasons': reasons,
                'price_change_1d': price_change_1d,
                'price_change_5d': price_change_5d,
                'volume_ratio': float(latest['Volume'] / avg_volume),
                'timestamp': datetime.now()
            }
            
        except Exception as e:
            return {'symbol': symbol, 'opportunity': False, 'error': str(e)}
    
    def scan_gap(self, symbol: str, df: pd.DataFrame, intraday_df: pd.DataFrame) -> Dict:
        """Scan for gap opportunities"""
        try:
            if len(df) < 2:
                return {'symbol': symbol, 'opportunity': False}
            
            today = df.iloc[-1]
            yesterday = df.iloc[-2]
            
            # Calculate gap
            gap_percent = (today['Open'] - yesterday['Close']) / yesterday['Close'] * 100
            
            gap_score = 0
            reasons = []
            
            # Significant gap
            if abs(gap_percent) > 2:
                gap_score += 40
                gap_type = "Gap up" if gap_percent > 0 else "Gap down"
                reasons.append(f"{gap_type} {abs(gap_percent):.1f}%")
            
            # Volume on gap
            avg_volume = df['Volume'].rolling(10).mean().iloc[-2]
            if today['Volume'] > avg_volume * 1.5:
                gap_score += 30
                reasons.append("High volume on gap")
            
            # Gap fill potential
            if gap_percent > 0:  # Gap up
                # Check if price is holding above yesterday's close
                if today['Low'] > yesterday['Close']:
                    gap_score += 20
                    reasons.append("Gap holding - no fill")
            else:  # Gap down
                # Check if price is bouncing from gap
                if today['High'] < yesterday['Close']:
                    gap_score += 20
                    reasons.append("Gap down holding")
            
            # News/earnings catalyst check (simplified)
            if abs(gap_percent) > 5:
                gap_score += 10
                reasons.append("Potential news catalyst")
            
            opportunity = gap_score >= 50 and abs(gap_percent) > 1
            
            return {
                'symbol': symbol,
                'opportunity': opportunity,
                'scan_type': 'gap',
                'score': gap_score,
                'current_price': float(today['Close']),
                'gap_percent': gap_percent,
                'reasons': reasons,
                'volume_ratio': float(today['Volume'] / avg_volume),
                'timestamp': datetime.now()
            }
            
        except Exception as e:
            return {'symbol': symbol, 'opportunity': False, 'error': str(e)}
    
    def scan_volume(self, symbol: str, df: pd.DataFrame, intraday_df: pd.DataFrame) -> Dict:
        """Scan for unusual volume opportunities"""
        try:
            latest = df.iloc[-1]
            
            volume_score = 0
            reasons = []
            
            # Volume surge
            avg_volume_10d = df['Volume'].rolling(10).mean().iloc[-1]
            avg_volume_30d = df['Volume'].rolling(30).mean().iloc[-1]
            
            volume_ratio_10d = latest['Volume'] / avg_volume_10d
            volume_ratio_30d = latest['Volume'] / avg_volume_30d
            
            if volume_ratio_10d > 3:
                volume_score += 50
                reasons.append(f"Extreme volume surge ({volume_ratio_10d:.1f}x)")
            elif volume_ratio_10d > 2:
                volume_score += 35
                reasons.append(f"High volume surge ({volume_ratio_10d:.1f}x)")
            elif volume_ratio_10d > 1.5:
                volume_score += 20
                reasons.append(f"Increased volume ({volume_ratio_10d:.1f}x)")
            
            # Price movement with volume
            price_change = (latest['Close'] - latest['Open']) / latest['Open'] * 100
            if abs(price_change) > 1 and volume_ratio_10d > 1.5:
                volume_score += 25
                reasons.append(f"Price movement with volume ({price_change:+.1f}%)")
            
            # Intraday volume pattern
            if not intraday_df.empty and len(intraday_df) > 10:
                recent_volume = intraday_df['Volume'].tail(10).sum()
                earlier_volume = intraday_df['Volume'].head(10).sum()
                
                if recent_volume > earlier_volume * 1.5:
                    volume_score += 15
                    reasons.append("Accelerating intraday volume")
            
            opportunity = volume_score >= 40
            
            return {
                'symbol': symbol,
                'opportunity': opportunity,
                'scan_type': 'volume',
                'score': volume_score,
                'current_price': float(latest['Close']),
                'reasons': reasons,
                'volume_ratio_10d': volume_ratio_10d,
                'volume_ratio_30d': volume_ratio_30d,
                'price_change': price_change,
                'timestamp': datetime.now()
            }
            
        except Exception as e:
            return {'symbol': symbol, 'opportunity': False, 'error': str(e)}
    
    def get_scan_results(self, scan_type: str = None) -> Dict:
        """Get latest scan results"""
        if scan_type:
            return self.scan_results.get(scan_type, {})
        return self.scan_results
    
    def get_top_opportunities(self, limit: int = 10) -> List[Dict]:
        """Get top opportunities across all scan types"""
        all_opportunities = []
        
        for scan_type, scan_data in self.scan_results.items():
            if 'results' in scan_data:
                for opportunity in scan_data['results']:
                    opportunity['scan_type'] = scan_type
                    all_opportunities.append(opportunity)
        
        # Sort by score
        all_opportunities.sort(key=lambda x: x.get('score', 0), reverse=True)
        
        return all_opportunities[:limit]
