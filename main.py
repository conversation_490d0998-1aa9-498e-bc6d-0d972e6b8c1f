"""
AI-Powered Trading Analysis Backend
Main FastAPI application for comprehensive stock analysis and trading signals
"""
from fastapi import FastAPI, HTTPException, BackgroundTasks, Query
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Dict, Optional
import asyncio
import pandas as pd
from datetime import datetime, <PERSON><PERSON><PERSON>
from loguru import logger
import uvicorn

# Import our custom modules
from config import Config
from utils.data_collector import DataCollector
from utils.company_validator import CompanyValidator
from utils.technical_analysis import TechnicalAnalyzer
from utils.timeframe_analyzer import TimeframeAnalyzer
from utils.signal_generator import SignalGenerator
from models.ai_predictor import AIPredictor

# Initialize FastAPI app
app = FastAPI(
    title="AI Trading Analysis System",
    description="Comprehensive AI-powered trading analysis with company validation, technical analysis, and predictive modeling",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize components
config = Config()
data_collector = DataCollector()
company_validator = CompanyValidator()
technical_analyzer = TechnicalAnalyzer()
timeframe_analyzer = TimeframeAnalyzer()
signal_generator = SignalGenerator()
ai_predictor = AIPredictor()

# Configure logging
logger.add(config.LOG_FILE, rotation="1 day", retention="30 days", level=config.LOG_LEVEL)

# Pydantic models for API requests/responses
class AnalysisRequest(BaseModel):
    symbol: str
    period: str = "1y"
    interval: str = "1d"

class BatchAnalysisRequest(BaseModel):
    symbols: List[str]
    period: str = "1y"
    interval: str = "1d"

class PredictionRequest(BaseModel):
    symbol: str
    days_ahead: int = 1
    period: str = "2y"

class CompanyValidationResponse(BaseModel):
    symbol: str
    is_legitimate: bool
    legitimacy_score: float
    risk_level: str
    red_flags: List[str]

class TradingSignalResponse(BaseModel):
    symbol: str
    primary_signal: str
    confidence: float
    risk_level: str
    entry_price: float
    stop_loss: float
    take_profit: float
    reasoning: List[str]

@app.get("/")
async def root():
    """Root endpoint with API information"""
    return {
        "message": "AI Trading Analysis System",
        "version": "1.0.0",
        "status": "active",
        "endpoints": {
            "company_validation": "/validate/{symbol}",
            "technical_analysis": "/technical/{symbol}",
            "trading_signals": "/signals/{symbol}",
            "price_prediction": "/predict/{symbol}",
            "temporal_analysis": "/temporal/{symbol}",
            "batch_analysis": "/batch/analyze",
            "health_check": "/health"
        }
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": datetime.now(),
        "components": {
            "data_collector": "active",
            "ai_predictor": "active",
            "signal_generator": "active"
        }
    }

@app.get("/validate/{symbol}", response_model=CompanyValidationResponse)
async def validate_company(symbol: str):
    """
    Validate company legitimacy and assess risk

    Args:
        symbol: Stock symbol to validate

    Returns:
        Company validation results with legitimacy score and risk assessment
    """
    try:
        logger.info(f"Validating company: {symbol}")

        # Validate company
        validation_result = company_validator.validate_company_legitimacy(symbol.upper())

        if not validation_result:
            raise HTTPException(status_code=404, detail=f"Could not validate company {symbol}")

        # Get risk assessment
        risk_assessment = company_validator.get_risk_assessment(symbol.upper())

        return CompanyValidationResponse(
            symbol=symbol.upper(),
            is_legitimate=validation_result.get('is_legitimate', False),
            legitimacy_score=validation_result.get('legitimacy_score', 0.0),
            risk_level=risk_assessment.get('risk_level', 'UNKNOWN'),
            red_flags=validation_result.get('red_flags', [])
        )

    except Exception as e:
        logger.error(f"Error validating company {symbol}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Validation failed: {str(e)}")

@app.get("/technical/{symbol}")
async def get_technical_analysis(
    symbol: str,
    period: str = Query("1y", description="Data period (1d, 5d, 1mo, 3mo, 6mo, 1y, 2y, 5y, 10y)"),
    interval: str = Query("1d", description="Data interval (1m, 2m, 5m, 15m, 30m, 60m, 90m, 1h, 1d, 5d, 1wk, 1mo)")
):
    """
    Get comprehensive technical analysis for a symbol

    Args:
        symbol: Stock symbol
        period: Data period
        interval: Data interval

    Returns:
        Technical analysis results including indicators, patterns, and trend analysis
    """
    try:
        logger.info(f"Performing technical analysis for {symbol}")

        # Get historical data
        df = data_collector.get_historical_data(symbol.upper(), period, interval)

        if df.empty:
            raise HTTPException(status_code=404, detail=f"No data found for symbol {symbol}")

        # Calculate technical indicators
        df_with_indicators = technical_analyzer.calculate_indicators(df)
        df_with_patterns = technical_analyzer.detect_candlestick_patterns(df_with_indicators)

        # Perform analysis
        trend_analysis = technical_analyzer.analyze_trend(df_with_patterns)
        pattern_summary = technical_analyzer.get_pattern_summary(df_with_patterns)
        support_resistance = technical_analyzer.detect_support_resistance(df_with_patterns)
        volatility = technical_analyzer.calculate_volatility(df_with_patterns)

        return {
            "symbol": symbol.upper(),
            "timestamp": datetime.now(),
            "data_points": len(df),
            "current_price": float(df['Close'].iloc[-1]),
            "trend_analysis": trend_analysis,
            "pattern_summary": pattern_summary,
            "support_resistance": support_resistance,
            "volatility": volatility,
            "latest_indicators": {
                "rsi": float(df_with_indicators['RSI'].iloc[-1]) if 'RSI' in df_with_indicators.columns else None,
                "macd": float(df_with_indicators['MACD'].iloc[-1]) if 'MACD' in df_with_indicators.columns else None,
                "sma_20": float(df_with_indicators['SMA_20'].iloc[-1]) if 'SMA_20' in df_with_indicators.columns else None,
                "sma_50": float(df_with_indicators['SMA_50'].iloc[-1]) if 'SMA_50' in df_with_indicators.columns else None
            }
        }

    except Exception as e:
        logger.error(f"Error in technical analysis for {symbol}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Technical analysis failed: {str(e)}")

@app.get("/signals/{symbol}", response_model=TradingSignalResponse)
async def get_trading_signals(
    symbol: str,
    period: str = Query("1y", description="Data period for analysis"),
    interval: str = Query("1d", description="Data interval")
):
    """
    Generate comprehensive trading signals for a symbol

    Args:
        symbol: Stock symbol
        period: Data period
        interval: Data interval

    Returns:
        Comprehensive trading signals with buy/sell recommendations
    """
    try:
        logger.info(f"Generating trading signals for {symbol}")

        # Get historical data
        df = data_collector.get_historical_data(symbol.upper(), period, interval)

        if df.empty:
            raise HTTPException(status_code=404, detail=f"No data found for symbol {symbol}")

        # Generate comprehensive signals
        signals = signal_generator.generate_comprehensive_signals(symbol.upper(), df)

        return TradingSignalResponse(
            symbol=signals['symbol'],
            primary_signal=signals['primary_signal'],
            confidence=signals['confidence'],
            risk_level=signals['risk_level'],
            entry_price=signals['entry_price'],
            stop_loss=signals['stop_loss'],
            take_profit=signals['take_profit'],
            reasoning=signals['reasoning']
        )

    except Exception as e:
        logger.error(f"Error generating signals for {symbol}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Signal generation failed: {str(e)}")

@app.get("/predict/{symbol}")
async def predict_price(
    symbol: str,
    days_ahead: int = Query(1, description="Number of days to predict ahead (1-5)"),
    period: str = Query("2y", description="Historical data period for training")
):
    """
    Predict future price using AI models

    Args:
        symbol: Stock symbol
        days_ahead: Number of days to predict ahead
        period: Historical data period

    Returns:
        Price predictions from multiple AI models
    """
    try:
        logger.info(f"Predicting price for {symbol}, {days_ahead} days ahead")

        if days_ahead < 1 or days_ahead > 5:
            raise HTTPException(status_code=400, detail="days_ahead must be between 1 and 5")

        # Get historical data
        df = data_collector.get_historical_data(symbol.upper(), period, "1d")

        if df.empty or len(df) < 100:
            raise HTTPException(status_code=404, detail=f"Insufficient data for prediction of {symbol}")

        # Train models and make predictions
        current_price = float(df['Close'].iloc[-1])

        # Train regression models
        training_results = ai_predictor.train_regression_models(df)

        # Get predictions
        price_predictions = ai_predictor.predict_price(df, days_ahead)
        trend_prediction = ai_predictor.predict_trend_direction(df)

        return {
            "symbol": symbol.upper(),
            "timestamp": datetime.now(),
            "current_price": current_price,
            "days_ahead": days_ahead,
            "price_predictions": price_predictions,
            "trend_prediction": trend_prediction,
            "model_performance": {k: {"r2": v["r2"], "rmse": v["rmse"]} for k, v in training_results.items()},
            "prediction_confidence": trend_prediction.get('confidence', 0.5)
        }

    except Exception as e:
        logger.error(f"Error predicting price for {symbol}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Price prediction failed: {str(e)}")

@app.get("/temporal/{symbol}")
async def get_temporal_analysis(
    symbol: str,
    period: str = Query("1y", description="Data period for analysis")
):
    """
    Get temporal analysis (yesterday, today, tomorrow trends)

    Args:
        symbol: Stock symbol
        period: Data period

    Returns:
        Temporal trend analysis across multiple timeframes
    """
    try:
        logger.info(f"Performing temporal analysis for {symbol}")

        # Get historical data
        df = data_collector.get_historical_data(symbol.upper(), period, "1d")

        if df.empty:
            raise HTTPException(status_code=404, detail=f"No data found for symbol {symbol}")

        # Perform temporal analysis
        temporal_analysis = timeframe_analyzer.analyze_temporal_trends(df, symbol.upper())

        return {
            "symbol": symbol.upper(),
            "timestamp": datetime.now(),
            "temporal_analysis": temporal_analysis
        }

    except Exception as e:
        logger.error(f"Error in temporal analysis for {symbol}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Temporal analysis failed: {str(e)}")

@app.post("/batch/analyze")
async def batch_analysis(request: BatchAnalysisRequest):
    """
    Perform batch analysis on multiple symbols

    Args:
        request: Batch analysis request with list of symbols

    Returns:
        Analysis results for all requested symbols
    """
    try:
        logger.info(f"Performing batch analysis for {len(request.symbols)} symbols")

        if len(request.symbols) > config.MAX_SYMBOLS_PER_REQUEST:
            raise HTTPException(
                status_code=400,
                detail=f"Too many symbols. Maximum allowed: {config.MAX_SYMBOLS_PER_REQUEST}"
            )

        # Collect data for all symbols
        data_dict = {}
        for symbol in request.symbols:
            try:
                df = data_collector.get_historical_data(symbol.upper(), request.period, request.interval)
                if not df.empty:
                    data_dict[symbol.upper()] = df
            except Exception as e:
                logger.warning(f"Failed to get data for {symbol}: {str(e)}")
                continue

        # Generate signals for all symbols
        batch_results = signal_generator.generate_batch_signals(list(data_dict.keys()), data_dict)

        return {
            "timestamp": datetime.now(),
            "symbols_analyzed": len(batch_results),
            "symbols_requested": len(request.symbols),
            "results": batch_results
        }

    except Exception as e:
        logger.error(f"Error in batch analysis: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Batch analysis failed: {str(e)}")

@app.get("/market/overview")
async def get_market_overview():
    """
    Get market overview with analysis of default symbols

    Returns:
        Market overview with signals for major stocks
    """
    try:
        logger.info("Generating market overview")

        # Analyze default symbols
        symbols = config.DEFAULT_SYMBOLS

        # Collect data
        data_dict = {}
        for symbol in symbols:
            try:
                df = data_collector.get_historical_data(symbol, "6mo", "1d")
                if not df.empty:
                    data_dict[symbol] = df
            except Exception as e:
                logger.warning(f"Failed to get data for {symbol}: {str(e)}")
                continue

        # Generate signals
        market_signals = signal_generator.generate_batch_signals(list(data_dict.keys()), data_dict)

        # Summarize market sentiment
        buy_signals = len([s for s in market_signals.values() if 'BUY' in s.get('primary_signal', '')])
        sell_signals = len([s for s in market_signals.values() if 'SELL' in s.get('primary_signal', '')])
        total_signals = len(market_signals)

        market_sentiment = "NEUTRAL"
        if buy_signals > sell_signals * 1.5:
            market_sentiment = "BULLISH"
        elif sell_signals > buy_signals * 1.5:
            market_sentiment = "BEARISH"

        return {
            "timestamp": datetime.now(),
            "market_sentiment": market_sentiment,
            "symbols_analyzed": total_signals,
            "buy_signals": buy_signals,
            "sell_signals": sell_signals,
            "neutral_signals": total_signals - buy_signals - sell_signals,
            "individual_signals": market_signals
        }

    except Exception as e:
        logger.error(f"Error generating market overview: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Market overview failed: {str(e)}")

if __name__ == "__main__":
    logger.info("Starting AI Trading Analysis System")
    uvicorn.run(
        "main:app",
        host=config.HOST,
        port=config.PORT,
        reload=config.DEBUG,
        log_level=config.LOG_LEVEL.lower()
    )