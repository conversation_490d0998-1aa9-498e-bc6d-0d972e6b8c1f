"""
Portfolio Tracker for managing trading positions and performance
"""
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from loguru import logger
import json
import yfinance as yf
from dataclasses import dataclass, asdict
from enum import Enum

class PositionType(Enum):
    LONG = "LONG"
    SHORT = "SHORT"

class PositionStatus(Enum):
    OPEN = "OPEN"
    CLOSED = "CLOSED"
    PARTIAL = "PARTIAL"

@dataclass
class Position:
    symbol: str
    position_type: PositionType
    entry_price: float
    quantity: int
    entry_date: datetime
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None
    current_price: Optional[float] = None
    status: PositionStatus = PositionStatus.OPEN
    exit_price: Optional[float] = None
    exit_date: Optional[datetime] = None
    notes: str = ""
    
    def to_dict(self):
        data = asdict(self)
        # Convert enums to strings
        data['position_type'] = self.position_type.value
        data['status'] = self.status.value
        # Convert datetime to ISO string
        data['entry_date'] = self.entry_date.isoformat()
        if self.exit_date:
            data['exit_date'] = self.exit_date.isoformat()
        return data

class PortfolioTracker:
    def __init__(self, initial_capital: float = 100000):
        self.initial_capital = initial_capital
        self.current_capital = initial_capital
        self.positions = {}  # {position_id: Position}
        self.closed_positions = {}  # {position_id: Position}
        self.trade_history = []
        self.portfolio_history = []
        self.next_position_id = 1
        
        # Performance metrics
        self.total_trades = 0
        self.winning_trades = 0
        self.losing_trades = 0
        self.total_pnl = 0.0
        self.max_drawdown = 0.0
        self.peak_value = initial_capital
        
    def add_position(self, symbol: str, position_type: str, entry_price: float, 
                    quantity: int, stop_loss: float = None, take_profit: float = None,
                    notes: str = "") -> str:
        """Add a new position to the portfolio"""
        position_id = f"POS_{self.next_position_id:04d}"
        self.next_position_id += 1
        
        position = Position(
            symbol=symbol,
            position_type=PositionType(position_type.upper()),
            entry_price=entry_price,
            quantity=quantity,
            entry_date=datetime.now(),
            stop_loss=stop_loss,
            take_profit=take_profit,
            notes=notes
        )
        
        self.positions[position_id] = position
        
        # Calculate position value
        position_value = entry_price * quantity
        self.current_capital -= position_value
        
        # Log trade
        trade_record = {
            'position_id': position_id,
            'action': 'OPEN',
            'symbol': symbol,
            'type': position_type,
            'price': entry_price,
            'quantity': quantity,
            'value': position_value,
            'timestamp': datetime.now(),
            'notes': notes
        }
        self.trade_history.append(trade_record)
        
        logger.info(f"Opened position {position_id}: {quantity} shares of {symbol} at ${entry_price:.2f}")
        return position_id
    
    def close_position(self, position_id: str, exit_price: float, quantity: int = None, 
                      notes: str = "") -> Dict:
        """Close a position (fully or partially)"""
        if position_id not in self.positions:
            raise ValueError(f"Position {position_id} not found")
        
        position = self.positions[position_id]
        
        # Default to full quantity if not specified
        if quantity is None:
            quantity = position.quantity
        
        if quantity > position.quantity:
            raise ValueError(f"Cannot close {quantity} shares, only {position.quantity} available")
        
        # Calculate P&L
        if position.position_type == PositionType.LONG:
            pnl = (exit_price - position.entry_price) * quantity
        else:  # SHORT
            pnl = (position.entry_price - exit_price) * quantity
        
        # Update capital
        position_value = exit_price * quantity
        self.current_capital += position_value
        self.total_pnl += pnl
        
        # Update position
        if quantity == position.quantity:
            # Full close
            position.status = PositionStatus.CLOSED
            position.exit_price = exit_price
            position.exit_date = datetime.now()
            
            # Move to closed positions
            self.closed_positions[position_id] = position
            del self.positions[position_id]
        else:
            # Partial close
            position.quantity -= quantity
            position.status = PositionStatus.PARTIAL
        
        # Update trade statistics
        self.total_trades += 1
        if pnl > 0:
            self.winning_trades += 1
        else:
            self.losing_trades += 1
        
        # Log trade
        trade_record = {
            'position_id': position_id,
            'action': 'CLOSE',
            'symbol': position.symbol,
            'price': exit_price,
            'quantity': quantity,
            'pnl': pnl,
            'value': position_value,
            'timestamp': datetime.now(),
            'notes': notes
        }
        self.trade_history.append(trade_record)
        
        logger.info(f"Closed {quantity} shares of {position.symbol} at ${exit_price:.2f}, P&L: ${pnl:.2f}")
        
        return {
            'position_id': position_id,
            'pnl': pnl,
            'exit_price': exit_price,
            'quantity_closed': quantity,
            'remaining_quantity': position.quantity if position_id in self.positions else 0
        }
    
    def update_positions(self):
        """Update current prices and unrealized P&L for all positions"""
        for position_id, position in self.positions.items():
            try:
                # Get current price
                ticker = yf.Ticker(position.symbol)
                current_data = ticker.history(period="1d", interval="1m")
                
                if not current_data.empty:
                    position.current_price = float(current_data['Close'].iloc[-1])
                    
                    # Check stop loss and take profit
                    self.check_exit_conditions(position_id, position)
                    
            except Exception as e:
                logger.error(f"Error updating position {position_id}: {str(e)}")
    
    def check_exit_conditions(self, position_id: str, position: Position):
        """Check if position should be automatically closed"""
        if not position.current_price:
            return
        
        should_close = False
        exit_reason = ""
        
        if position.position_type == PositionType.LONG:
            # Check stop loss
            if position.stop_loss and position.current_price <= position.stop_loss:
                should_close = True
                exit_reason = "Stop loss triggered"
            
            # Check take profit
            elif position.take_profit and position.current_price >= position.take_profit:
                should_close = True
                exit_reason = "Take profit triggered"
        
        else:  # SHORT position
            # Check stop loss (price going up)
            if position.stop_loss and position.current_price >= position.stop_loss:
                should_close = True
                exit_reason = "Stop loss triggered"
            
            # Check take profit (price going down)
            elif position.take_profit and position.current_price <= position.take_profit:
                should_close = True
                exit_reason = "Take profit triggered"
        
        if should_close:
            logger.warning(f"Auto-closing position {position_id}: {exit_reason}")
            self.close_position(position_id, position.current_price, notes=exit_reason)
    
    def get_portfolio_value(self) -> float:
        """Calculate total portfolio value"""
        total_value = self.current_capital
        
        for position in self.positions.values():
            if position.current_price:
                position_value = position.current_price * position.quantity
                total_value += position_value
        
        return total_value
    
    def get_unrealized_pnl(self) -> float:
        """Calculate total unrealized P&L"""
        unrealized_pnl = 0.0
        
        for position in self.positions.values():
            if position.current_price:
                if position.position_type == PositionType.LONG:
                    pnl = (position.current_price - position.entry_price) * position.quantity
                else:  # SHORT
                    pnl = (position.entry_price - position.current_price) * position.quantity
                
                unrealized_pnl += pnl
        
        return unrealized_pnl
    
    def get_realized_pnl(self) -> float:
        """Get total realized P&L from closed positions"""
        return self.total_pnl
    
    def get_performance_metrics(self) -> Dict:
        """Calculate comprehensive performance metrics"""
        portfolio_value = self.get_portfolio_value()
        total_return = (portfolio_value - self.initial_capital) / self.initial_capital * 100
        
        # Update peak value and drawdown
        if portfolio_value > self.peak_value:
            self.peak_value = portfolio_value
        
        current_drawdown = (self.peak_value - portfolio_value) / self.peak_value * 100
        if current_drawdown > self.max_drawdown:
            self.max_drawdown = current_drawdown
        
        # Win rate
        win_rate = (self.winning_trades / self.total_trades * 100) if self.total_trades > 0 else 0
        
        # Average win/loss
        winning_trades_pnl = [trade['pnl'] for trade in self.trade_history 
                             if trade.get('pnl', 0) > 0]
        losing_trades_pnl = [trade['pnl'] for trade in self.trade_history 
                            if trade.get('pnl', 0) < 0]
        
        avg_win = np.mean(winning_trades_pnl) if winning_trades_pnl else 0
        avg_loss = np.mean(losing_trades_pnl) if losing_trades_pnl else 0
        
        # Profit factor
        total_wins = sum(winning_trades_pnl) if winning_trades_pnl else 0
        total_losses = abs(sum(losing_trades_pnl)) if losing_trades_pnl else 0
        profit_factor = total_wins / total_losses if total_losses > 0 else float('inf')
        
        return {
            'initial_capital': self.initial_capital,
            'current_capital': self.current_capital,
            'portfolio_value': portfolio_value,
            'total_return_pct': total_return,
            'realized_pnl': self.get_realized_pnl(),
            'unrealized_pnl': self.get_unrealized_pnl(),
            'total_trades': self.total_trades,
            'winning_trades': self.winning_trades,
            'losing_trades': self.losing_trades,
            'win_rate_pct': win_rate,
            'avg_win': avg_win,
            'avg_loss': avg_loss,
            'profit_factor': profit_factor,
            'max_drawdown_pct': self.max_drawdown,
            'current_drawdown_pct': current_drawdown,
            'open_positions': len(self.positions),
            'closed_positions': len(self.closed_positions)
        }
    
    def get_position_summary(self) -> List[Dict]:
        """Get summary of all open positions"""
        summary = []
        
        for position_id, position in self.positions.items():
            # Calculate current P&L
            if position.current_price:
                if position.position_type == PositionType.LONG:
                    unrealized_pnl = (position.current_price - position.entry_price) * position.quantity
                    pnl_pct = (position.current_price - position.entry_price) / position.entry_price * 100
                else:  # SHORT
                    unrealized_pnl = (position.entry_price - position.current_price) * position.quantity
                    pnl_pct = (position.entry_price - position.current_price) / position.entry_price * 100
            else:
                unrealized_pnl = 0
                pnl_pct = 0
            
            summary.append({
                'position_id': position_id,
                'symbol': position.symbol,
                'type': position.position_type.value,
                'quantity': position.quantity,
                'entry_price': position.entry_price,
                'current_price': position.current_price,
                'unrealized_pnl': unrealized_pnl,
                'pnl_pct': pnl_pct,
                'stop_loss': position.stop_loss,
                'take_profit': position.take_profit,
                'entry_date': position.entry_date,
                'status': position.status.value,
                'notes': position.notes
            })
        
        return summary
    
    def get_trade_history(self, limit: int = None) -> List[Dict]:
        """Get trade history"""
        history = sorted(self.trade_history, key=lambda x: x['timestamp'], reverse=True)
        
        if limit:
            history = history[:limit]
        
        # Convert datetime objects to strings for JSON serialization
        for trade in history:
            if isinstance(trade['timestamp'], datetime):
                trade['timestamp'] = trade['timestamp'].isoformat()
        
        return history
    
    def get_daily_pnl(self, days: int = 30) -> List[Dict]:
        """Get daily P&L for the last N days"""
        end_date = datetime.now().date()
        start_date = end_date - timedelta(days=days)
        
        daily_pnl = []
        current_date = start_date
        
        while current_date <= end_date:
            # Calculate P&L for this date
            day_trades = [trade for trade in self.trade_history 
                         if trade['timestamp'].date() == current_date and 'pnl' in trade]
            
            day_pnl = sum(trade['pnl'] for trade in day_trades)
            
            daily_pnl.append({
                'date': current_date.isoformat(),
                'pnl': day_pnl,
                'trades': len(day_trades)
            })
            
            current_date += timedelta(days=1)
        
        return daily_pnl
    
    def save_portfolio(self, filename: str):
        """Save portfolio to JSON file"""
        portfolio_data = {
            'initial_capital': self.initial_capital,
            'current_capital': self.current_capital,
            'positions': {pid: pos.to_dict() for pid, pos in self.positions.items()},
            'closed_positions': {pid: pos.to_dict() for pid, pos in self.closed_positions.items()},
            'trade_history': self.get_trade_history(),
            'performance_metrics': self.get_performance_metrics(),
            'saved_at': datetime.now().isoformat()
        }
        
        with open(filename, 'w') as f:
            json.dump(portfolio_data, f, indent=2, default=str)
        
        logger.info(f"Portfolio saved to {filename}")
    
    def load_portfolio(self, filename: str):
        """Load portfolio from JSON file"""
        with open(filename, 'r') as f:
            portfolio_data = json.load(f)
        
        self.initial_capital = portfolio_data['initial_capital']
        self.current_capital = portfolio_data['current_capital']
        
        # Reconstruct positions (simplified - would need full reconstruction in production)
        logger.info(f"Portfolio loaded from {filename}")
    
    def get_risk_metrics(self) -> Dict:
        """Calculate risk metrics"""
        portfolio_value = self.get_portfolio_value()
        
        # Position concentration
        position_values = []
        for position in self.positions.values():
            if position.current_price:
                value = position.current_price * position.quantity
                position_values.append(value)
        
        total_position_value = sum(position_values)
        max_position_pct = (max(position_values) / portfolio_value * 100) if position_values else 0
        
        # Sector/symbol concentration
        symbols = [pos.symbol for pos in self.positions.values()]
        unique_symbols = len(set(symbols))
        
        return {
            'portfolio_value': portfolio_value,
            'cash_pct': (self.current_capital / portfolio_value * 100),
            'invested_pct': (total_position_value / portfolio_value * 100),
            'max_position_pct': max_position_pct,
            'num_positions': len(self.positions),
            'num_unique_symbols': unique_symbols,
            'avg_position_size': (total_position_value / len(self.positions)) if self.positions else 0
        }
