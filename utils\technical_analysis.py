"""
Technical Analysis Engine for candlestick patterns and technical indicators
"""
import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional
from loguru import logger
import talib
from config import Config

class TechnicalAnalyzer:
    def __init__(self):
        self.config = Config()
        
    def calculate_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Calculate various technical indicators
        
        Args:
            df: DataFrame with OHLCV data
            
        Returns:
            DataFrame with added technical indicators
        """
        try:
            df = df.copy()
            
            # Moving Averages
            df['SMA_20'] = talib.SMA(df['Close'], timeperiod=20)
            df['SMA_50'] = talib.SMA(df['Close'], timeperiod=50)
            df['SMA_200'] = talib.SMA(df['Close'], timeperiod=200)
            df['EMA_12'] = talib.EMA(df['Close'], timeperiod=12)
            df['EMA_26'] = talib.EMA(df['Close'], timeperiod=26)
            
            # RSI
            df['RSI'] = talib.RSI(df['Close'], timeperiod=self.config.RSI_PERIOD)
            
            # MACD
            df['MACD'], df['MACD_Signal'], df['MACD_Hist'] = talib.MACD(
                df['Close'], 
                fastperiod=self.config.MACD_FAST,
                slowperiod=self.config.MACD_SLOW,
                signalperiod=self.config.MACD_SIGNAL
            )
            
            # Bollinger Bands
            df['BB_Upper'], df['BB_Middle'], df['BB_Lower'] = talib.BBANDS(
                df['Close'],
                timeperiod=self.config.BOLLINGER_PERIOD,
                nbdevup=self.config.BOLLINGER_STD,
                nbdevdn=self.config.BOLLINGER_STD
            )
            
            # Stochastic Oscillator
            df['Stoch_K'], df['Stoch_D'] = talib.STOCH(df['High'], df['Low'], df['Close'])
            
            # Average True Range (ATR)
            df['ATR'] = talib.ATR(df['High'], df['Low'], df['Close'], timeperiod=14)
            
            # Volume indicators
            df['Volume_SMA'] = talib.SMA(df['Volume'], timeperiod=20)
            df['OBV'] = talib.OBV(df['Close'], df['Volume'])
            
            # Williams %R
            df['Williams_R'] = talib.WILLR(df['High'], df['Low'], df['Close'], timeperiod=14)
            
            # Commodity Channel Index
            df['CCI'] = talib.CCI(df['High'], df['Low'], df['Close'], timeperiod=14)
            
            logger.info(f"Calculated technical indicators for {len(df)} data points")
            return df
            
        except Exception as e:
            logger.error(f"Error calculating technical indicators: {str(e)}")
            return df
    
    def detect_candlestick_patterns(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Detect various candlestick patterns
        
        Args:
            df: DataFrame with OHLCV data
            
        Returns:
            DataFrame with candlestick pattern columns
        """
        try:
            df = df.copy()
            
            # Bullish Patterns
            df['Hammer'] = talib.CDLHAMMER(df['Open'], df['High'], df['Low'], df['Close'])
            df['Doji'] = talib.CDLDOJI(df['Open'], df['High'], df['Low'], df['Close'])
            df['Bullish_Engulfing'] = talib.CDLENGULFING(df['Open'], df['High'], df['Low'], df['Close'])
            df['Morning_Star'] = talib.CDLMORNINGSTAR(df['Open'], df['High'], df['Low'], df['Close'])
            df['Piercing_Pattern'] = talib.CDLPIERCING(df['Open'], df['High'], df['Low'], df['Close'])
            df['Three_White_Soldiers'] = talib.CDL3WHITESOLDIERS(df['Open'], df['High'], df['Low'], df['Close'])
            df['Bullish_Harami'] = talib.CDLHARAMI(df['Open'], df['High'], df['Low'], df['Close'])
            
            # Bearish Patterns
            df['Hanging_Man'] = talib.CDLHANGINGMAN(df['Open'], df['High'], df['Low'], df['Close'])
            df['Shooting_Star'] = talib.CDLSHOOTINGSTAR(df['Open'], df['High'], df['Low'], df['Close'])
            df['Bearish_Engulfing'] = talib.CDLENGULFING(df['Open'], df['High'], df['Low'], df['Close']) * -1
            df['Evening_Star'] = talib.CDLEVENINGSTAR(df['Open'], df['High'], df['Low'], df['Close'])
            df['Dark_Cloud_Cover'] = talib.CDLDARKCLOUDCOVER(df['Open'], df['High'], df['Low'], df['Close'])
            df['Three_Black_Crows'] = talib.CDL3BLACKCROWS(df['Open'], df['High'], df['Low'], df['Close'])
            
            # Neutral/Reversal Patterns
            df['Spinning_Top'] = talib.CDLSPINNINGTOP(df['Open'], df['High'], df['Low'], df['Close'])
            df['Marubozu'] = talib.CDLMARUBOZU(df['Open'], df['High'], df['Low'], df['Close'])
            
            logger.info(f"Detected candlestick patterns for {len(df)} data points")
            return df
            
        except Exception as e:
            logger.error(f"Error detecting candlestick patterns: {str(e)}")
            return df
    
    def analyze_trend(self, df: pd.DataFrame) -> Dict:
        """
        Analyze current trend based on multiple indicators
        
        Args:
            df: DataFrame with technical indicators
            
        Returns:
            Dictionary with trend analysis
        """
        try:
            latest = df.iloc[-1]
            
            trend_signals = []
            
            # Moving Average Trend
            if latest['Close'] > latest['SMA_20'] > latest['SMA_50']:
                trend_signals.append('bullish')
            elif latest['Close'] < latest['SMA_20'] < latest['SMA_50']:
                trend_signals.append('bearish')
            else:
                trend_signals.append('neutral')
            
            # RSI Analysis
            rsi = latest['RSI']
            if rsi > 70:
                trend_signals.append('overbought')
            elif rsi < 30:
                trend_signals.append('oversold')
            else:
                trend_signals.append('neutral')
            
            # MACD Analysis
            if latest['MACD'] > latest['MACD_Signal']:
                trend_signals.append('bullish')
            else:
                trend_signals.append('bearish')
            
            # Bollinger Bands Analysis
            if latest['Close'] > latest['BB_Upper']:
                trend_signals.append('overbought')
            elif latest['Close'] < latest['BB_Lower']:
                trend_signals.append('oversold')
            else:
                trend_signals.append('neutral')
            
            # Overall trend determination
            bullish_count = trend_signals.count('bullish')
            bearish_count = trend_signals.count('bearish')
            
            if bullish_count > bearish_count:
                overall_trend = 'BULLISH'
            elif bearish_count > bullish_count:
                overall_trend = 'BEARISH'
            else:
                overall_trend = 'NEUTRAL'
            
            return {
                'overall_trend': overall_trend,
                'trend_strength': abs(bullish_count - bearish_count) / len(trend_signals),
                'rsi': rsi,
                'macd_signal': 'BUY' if latest['MACD'] > latest['MACD_Signal'] else 'SELL',
                'bb_position': self._get_bb_position(latest),
                'signals': trend_signals
            }
            
        except Exception as e:
            logger.error(f"Error analyzing trend: {str(e)}")
            return {'overall_trend': 'UNKNOWN', 'trend_strength': 0}
    
    def _get_bb_position(self, row) -> str:
        """Get position relative to Bollinger Bands"""
        if row['Close'] > row['BB_Upper']:
            return 'ABOVE_UPPER'
        elif row['Close'] < row['BB_Lower']:
            return 'BELOW_LOWER'
        elif row['Close'] > row['BB_Middle']:
            return 'UPPER_HALF'
        else:
            return 'LOWER_HALF'
    
    def detect_support_resistance(self, df: pd.DataFrame, window: int = 20) -> Dict:
        """
        Detect support and resistance levels
        
        Args:
            df: DataFrame with OHLCV data
            window: Window size for detection
            
        Returns:
            Dictionary with support and resistance levels
        """
        try:
            highs = df['High'].rolling(window=window).max()
            lows = df['Low'].rolling(window=window).min()
            
            # Find recent support and resistance
            recent_high = df['High'].tail(window).max()
            recent_low = df['Low'].tail(window).min()
            
            # Calculate pivot points
            pivot = (df['High'].iloc[-1] + df['Low'].iloc[-1] + df['Close'].iloc[-1]) / 3
            resistance1 = 2 * pivot - df['Low'].iloc[-1]
            support1 = 2 * pivot - df['High'].iloc[-1]
            
            return {
                'current_price': df['Close'].iloc[-1],
                'resistance_levels': [recent_high, resistance1],
                'support_levels': [recent_low, support1],
                'pivot_point': pivot
            }
            
        except Exception as e:
            logger.error(f"Error detecting support/resistance: {str(e)}")
            return {}
    
    def get_pattern_summary(self, df: pd.DataFrame) -> Dict:
        """
        Get summary of recent candlestick patterns
        
        Args:
            df: DataFrame with pattern data
            
        Returns:
            Dictionary with pattern summary
        """
        try:
            # Get last few rows to check for recent patterns
            recent_data = df.tail(5)
            
            bullish_patterns = ['Hammer', 'Bullish_Engulfing', 'Morning_Star', 
                              'Piercing_Pattern', 'Three_White_Soldiers', 'Bullish_Harami']
            bearish_patterns = ['Hanging_Man', 'Shooting_Star', 'Bearish_Engulfing',
                              'Evening_Star', 'Dark_Cloud_Cover', 'Three_Black_Crows']
            
            detected_patterns = []
            
            for pattern in bullish_patterns + bearish_patterns:
                if pattern in recent_data.columns:
                    pattern_occurrences = recent_data[pattern].abs().sum()
                    if pattern_occurrences > 0:
                        pattern_type = 'BULLISH' if pattern in bullish_patterns else 'BEARISH'
                        detected_patterns.append({
                            'pattern': pattern,
                            'type': pattern_type,
                            'strength': pattern_occurrences
                        })
            
            return {
                'detected_patterns': detected_patterns,
                'pattern_count': len(detected_patterns),
                'bullish_patterns': len([p for p in detected_patterns if p['type'] == 'BULLISH']),
                'bearish_patterns': len([p for p in detected_patterns if p['type'] == 'BEARISH'])
            }
            
        except Exception as e:
            logger.error(f"Error getting pattern summary: {str(e)}")
            return {'detected_patterns': [], 'pattern_count': 0}
    
    def calculate_volatility(self, df: pd.DataFrame, window: int = 20) -> Dict:
        """
        Calculate various volatility measures
        
        Args:
            df: DataFrame with price data
            window: Window for calculations
            
        Returns:
            Dictionary with volatility metrics
        """
        try:
            # Calculate returns
            df['Returns'] = df['Close'].pct_change()
            
            # Historical volatility
            hist_vol = df['Returns'].rolling(window=window).std() * np.sqrt(252)
            
            # Average True Range based volatility
            atr_vol = df['ATR'] / df['Close'] if 'ATR' in df.columns else None
            
            return {
                'historical_volatility': hist_vol.iloc[-1] if not hist_vol.empty else 0,
                'atr_volatility': atr_vol.iloc[-1] if atr_vol is not None else 0,
                'price_range_pct': ((df['High'].iloc[-1] - df['Low'].iloc[-1]) / df['Close'].iloc[-1]) * 100
            }
            
        except Exception as e:
            logger.error(f"Error calculating volatility: {str(e)}")
            return {'historical_volatility': 0, 'atr_volatility': 0, 'price_range_pct': 0}
