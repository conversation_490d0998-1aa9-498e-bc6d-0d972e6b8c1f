"""
Advanced AI-Powered Trend Analyzer with Buy/Sell Suggestions and Profit Predictions
"""
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
from loguru import logger
import asyncio
from concurrent.futures import ThreadPoolExecutor
import yfinance as yf
from sklearn.ensemble import RandomForestRegressor, GradientBoostingClassifier
from sklearn.preprocessing import StandardScaler
import joblib
import warnings
warnings.filterwarnings('ignore')

from utils.technical_analysis import TechnicalAnalyzer
from config import Config

class AITrendAnalyzer:
    def __init__(self):
        self.config = Config()
        self.technical_analyzer = TechnicalAnalyzer()
        self.models = {}
        self.scalers = {}
        self.model_cache = {}
        self.last_training = {}
        
    async def analyze_market_trends(self, symbol: str, timeframe: str = "1d") -> Dict:
        """
        Comprehensive AI-powered market trend analysis with buy/sell suggestions
        
        Args:
            symbol: Stock symbol to analyze
            timeframe: Analysis timeframe (1d, 1h, 5m)
            
        Returns:
            Complete analysis with predictions and profit potential
        """
        try:
            logger.info(f"Starting AI trend analysis for {symbol}")
            
            # Get market data
            df = await self._get_optimized_data(symbol, timeframe)
            if df.empty:
                return self._create_error_response(symbol, "No data available")
            
            # Prepare features for AI analysis
            features_df = await self._prepare_ai_features(df, symbol)
            
            # Train or load models
            await self._ensure_models_ready(symbol, features_df)
            
            # Generate predictions
            predictions = await self._generate_predictions(symbol, features_df)
            
            # Analyze trends
            trend_analysis = await self._analyze_trends(df, features_df)
            
            # Generate buy/sell suggestions
            suggestions = await self._generate_suggestions(symbol, predictions, trend_analysis, df)
            
            # Calculate profit potential
            profit_analysis = await self._calculate_profit_potential(symbol, suggestions, df)
            
            # Create comprehensive response
            return {
                'symbol': symbol,
                'timestamp': datetime.now().isoformat(),
                'current_price': float(df['Close'].iloc[-1]),
                'trend_analysis': trend_analysis,
                'predictions': predictions,
                'suggestions': suggestions,
                'profit_potential': profit_analysis,
                'confidence_score': suggestions.get('confidence', 0),
                'risk_level': self._assess_risk_level(trend_analysis, predictions),
                'market_sentiment': self._determine_market_sentiment(trend_analysis),
                'technical_indicators': self._get_key_indicators(features_df),
                'next_update': (datetime.now() + timedelta(minutes=5)).isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error in AI trend analysis for {symbol}: {str(e)}")
            return self._create_error_response(symbol, str(e))
    
    async def _get_optimized_data(self, symbol: str, timeframe: str) -> pd.DataFrame:
        """Get optimized market data with caching"""
        cache_key = f"{symbol}_{timeframe}"
        
        # Check cache
        if cache_key in self.model_cache:
            cached_data, cache_time = self.model_cache[cache_key]
            if datetime.now() - cache_time < timedelta(minutes=1):
                return cached_data
        
        # Fetch new data
        try:
            ticker = yf.Ticker(symbol)
            
            if timeframe == "1d":
                df = ticker.history(period="1y", interval="1d")
            elif timeframe == "1h":
                df = ticker.history(period="1mo", interval="1h")
            else:  # 5m
                df = ticker.history(period="5d", interval="5m")
            
            if not df.empty:
                # Cache the data
                self.model_cache[cache_key] = (df, datetime.now())
            
            return df
            
        except Exception as e:
            logger.error(f"Error fetching data for {symbol}: {str(e)}")
            return pd.DataFrame()
    
    async def _prepare_ai_features(self, df: pd.DataFrame, symbol: str) -> pd.DataFrame:
        """Prepare advanced features for AI models"""
        try:
            # Calculate technical indicators
            df_with_indicators = self.technical_analyzer.calculate_indicators(df)
            df_with_patterns = self.technical_analyzer.detect_candlestick_patterns(df_with_indicators)
            
            # Add advanced features
            features_df = df_with_patterns.copy()
            
            # Price momentum features
            for period in [5, 10, 20]:
                features_df[f'momentum_{period}'] = features_df['Close'].pct_change(period)
                features_df[f'volatility_{period}'] = features_df['Close'].rolling(period).std()
            
            # Volume features
            features_df['volume_sma_ratio'] = features_df['Volume'] / features_df['Volume'].rolling(20).mean()
            features_df['volume_momentum'] = features_df['Volume'].pct_change(5)
            
            # Price position features
            features_df['price_position_20'] = (features_df['Close'] - features_df['Close'].rolling(20).min()) / (features_df['Close'].rolling(20).max() - features_df['Close'].rolling(20).min())
            features_df['price_position_50'] = (features_df['Close'] - features_df['Close'].rolling(50).min()) / (features_df['Close'].rolling(50).max() - features_df['Close'].rolling(50).min())
            
            # Trend strength features
            features_df['trend_strength'] = np.abs(features_df['Close'].rolling(10).apply(lambda x: np.polyfit(range(len(x)), x, 1)[0]))
            
            # Market structure features
            features_df['higher_highs'] = (features_df['High'] > features_df['High'].shift(1)).rolling(5).sum()
            features_df['higher_lows'] = (features_df['Low'] > features_df['Low'].shift(1)).rolling(5).sum()
            
            # Target variables for training
            features_df['future_return_1d'] = features_df['Close'].shift(-1) / features_df['Close'] - 1
            features_df['future_return_3d'] = features_df['Close'].shift(-3) / features_df['Close'] - 1
            features_df['future_direction'] = (features_df['future_return_1d'] > 0).astype(int)
            
            return features_df.fillna(method='ffill').fillna(0)
            
        except Exception as e:
            logger.error(f"Error preparing features: {str(e)}")
            return df
    
    async def _ensure_models_ready(self, symbol: str, features_df: pd.DataFrame):
        """Ensure AI models are trained and ready"""
        model_key = f"{symbol}_model"
        
        # Check if models need retraining
        needs_training = (
            model_key not in self.models or
            model_key not in self.last_training or
            datetime.now() - self.last_training[model_key] > timedelta(hours=6)
        )
        
        if needs_training and len(features_df) > 100:
            await self._train_models(symbol, features_df)
    
    async def _train_models(self, symbol: str, features_df: pd.DataFrame):
        """Train AI models for predictions"""
        try:
            logger.info(f"Training AI models for {symbol}")
            
            # Select features for training
            feature_columns = [col for col in features_df.columns if col not in [
                'future_return_1d', 'future_return_3d', 'future_direction', 'Date'
            ] and features_df[col].dtype in ['float64', 'int64']]
            
            # Prepare training data
            train_data = features_df[feature_columns + ['future_return_1d', 'future_direction']].dropna()
            
            if len(train_data) < 50:
                logger.warning(f"Insufficient data for training {symbol}")
                return
            
            X = train_data[feature_columns]
            y_return = train_data['future_return_1d']
            y_direction = train_data['future_direction']
            
            # Scale features
            scaler = StandardScaler()
            X_scaled = scaler.fit_transform(X)
            
            # Train return prediction model
            return_model = RandomForestRegressor(n_estimators=100, random_state=42, n_jobs=-1)
            return_model.fit(X_scaled, y_return)
            
            # Train direction prediction model
            direction_model = GradientBoostingClassifier(n_estimators=100, random_state=42)
            direction_model.fit(X_scaled, y_direction)
            
            # Store models
            model_key = f"{symbol}_model"
            self.models[model_key] = {
                'return_model': return_model,
                'direction_model': direction_model,
                'feature_columns': feature_columns
            }
            self.scalers[model_key] = scaler
            self.last_training[model_key] = datetime.now()
            
            logger.info(f"Models trained successfully for {symbol}")
            
        except Exception as e:
            logger.error(f"Error training models for {symbol}: {str(e)}")
    
    async def _generate_predictions(self, symbol: str, features_df: pd.DataFrame) -> Dict:
        """Generate AI predictions"""
        try:
            model_key = f"{symbol}_model"
            
            if model_key not in self.models:
                return {'error': 'Models not available'}
            
            models = self.models[model_key]
            scaler = self.scalers[model_key]
            
            # Get latest features
            latest_features = features_df[models['feature_columns']].iloc[-1:].fillna(0)
            latest_scaled = scaler.transform(latest_features)
            
            # Make predictions
            return_pred = models['return_model'].predict(latest_scaled)[0]
            direction_prob = models['direction_model'].predict_proba(latest_scaled)[0]
            
            # Calculate confidence
            direction_confidence = max(direction_prob)
            
            # Predict multiple timeframes
            predictions = {
                '1_day': {
                    'return_prediction': float(return_pred),
                    'direction_probability': float(direction_prob[1]),  # Probability of up movement
                    'confidence': float(direction_confidence),
                    'expected_price': float(features_df['Close'].iloc[-1] * (1 + return_pred))
                },
                '3_day': {
                    'return_prediction': float(return_pred * 2.5),  # Scaled estimate
                    'expected_price': float(features_df['Close'].iloc[-1] * (1 + return_pred * 2.5))
                },
                '1_week': {
                    'return_prediction': float(return_pred * 4.5),  # Scaled estimate
                    'expected_price': float(features_df['Close'].iloc[-1] * (1 + return_pred * 4.5))
                }
            }
            
            return predictions
            
        except Exception as e:
            logger.error(f"Error generating predictions: {str(e)}")
            return {'error': str(e)}
    
    async def _analyze_trends(self, df: pd.DataFrame, features_df: pd.DataFrame) -> Dict:
        """Analyze market trends"""
        try:
            current_price = df['Close'].iloc[-1]
            
            # Short-term trend (5 days)
            short_trend = (current_price - df['Close'].iloc[-6]) / df['Close'].iloc[-6] * 100
            
            # Medium-term trend (20 days)
            medium_trend = (current_price - df['Close'].iloc[-21]) / df['Close'].iloc[-21] * 100
            
            # Long-term trend (50 days)
            long_trend = (current_price - df['Close'].iloc[-51]) / df['Close'].iloc[-51] * 100 if len(df) > 50 else 0
            
            # Trend strength
            trend_strength = features_df['trend_strength'].iloc[-1] if 'trend_strength' in features_df.columns else 0
            
            # Volume trend
            volume_trend = (df['Volume'].iloc[-5:].mean() - df['Volume'].iloc[-20:-5].mean()) / df['Volume'].iloc[-20:-5].mean() * 100
            
            return {
                'short_term_trend': {
                    'percentage': float(short_trend),
                    'direction': 'UP' if short_trend > 0 else 'DOWN',
                    'strength': 'STRONG' if abs(short_trend) > 5 else 'MODERATE' if abs(short_trend) > 2 else 'WEAK'
                },
                'medium_term_trend': {
                    'percentage': float(medium_trend),
                    'direction': 'UP' if medium_trend > 0 else 'DOWN',
                    'strength': 'STRONG' if abs(medium_trend) > 10 else 'MODERATE' if abs(medium_trend) > 5 else 'WEAK'
                },
                'long_term_trend': {
                    'percentage': float(long_trend),
                    'direction': 'UP' if long_trend > 0 else 'DOWN',
                    'strength': 'STRONG' if abs(long_trend) > 20 else 'MODERATE' if abs(long_trend) > 10 else 'WEAK'
                },
                'trend_strength': float(trend_strength),
                'volume_trend': float(volume_trend),
                'overall_trend': self._determine_overall_trend(short_trend, medium_trend, long_trend)
            }
            
        except Exception as e:
            logger.error(f"Error analyzing trends: {str(e)}")
            return {}
    
    async def _generate_suggestions(self, symbol: str, predictions: Dict, trend_analysis: Dict, df: pd.DataFrame) -> Dict:
        """Generate buy/sell suggestions with reasoning"""
        try:
            if 'error' in predictions:
                return {'action': 'HOLD', 'confidence': 0, 'reasoning': ['Insufficient data for analysis']}
            
            current_price = df['Close'].iloc[-1]
            pred_1d = predictions.get('1_day', {})
            
            # Extract prediction data
            return_pred = pred_1d.get('return_prediction', 0)
            direction_prob = pred_1d.get('direction_probability', 0.5)
            confidence = pred_1d.get('confidence', 0)
            
            # Decision logic
            reasoning = []
            action = 'HOLD'
            
            # Strong buy conditions
            if return_pred > 0.03 and direction_prob > 0.7 and confidence > 0.7:
                action = 'STRONG_BUY'
                reasoning.append(f"AI predicts {return_pred*100:.1f}% gain with {confidence*100:.0f}% confidence")
            
            # Buy conditions
            elif return_pred > 0.015 and direction_prob > 0.6:
                action = 'BUY'
                reasoning.append(f"Positive return prediction: {return_pred*100:.1f}%")
            
            # Strong sell conditions
            elif return_pred < -0.03 and direction_prob < 0.3 and confidence > 0.7:
                action = 'STRONG_SELL'
                reasoning.append(f"AI predicts {abs(return_pred)*100:.1f}% decline with {confidence*100:.0f}% confidence")
            
            # Sell conditions
            elif return_pred < -0.015 and direction_prob < 0.4:
                action = 'SELL'
                reasoning.append(f"Negative return prediction: {return_pred*100:.1f}%")
            
            # Add trend-based reasoning
            overall_trend = trend_analysis.get('overall_trend', 'NEUTRAL')
            if overall_trend == 'BULLISH' and action in ['BUY', 'STRONG_BUY']:
                reasoning.append("Trend analysis supports bullish outlook")
            elif overall_trend == 'BEARISH' and action in ['SELL', 'STRONG_SELL']:
                reasoning.append("Trend analysis supports bearish outlook")
            
            # Add technical reasoning
            if 'RSI' in df.columns:
                rsi = df['RSI'].iloc[-1]
                if rsi < 30 and action in ['BUY', 'STRONG_BUY']:
                    reasoning.append(f"RSI oversold ({rsi:.1f})")
                elif rsi > 70 and action in ['SELL', 'STRONG_SELL']:
                    reasoning.append(f"RSI overbought ({rsi:.1f})")
            
            return {
                'action': action,
                'confidence': float(confidence),
                'reasoning': reasoning,
                'entry_price': float(current_price),
                'target_price': pred_1d.get('expected_price', current_price),
                'stop_loss': float(current_price * 0.95) if action in ['BUY', 'STRONG_BUY'] else float(current_price * 1.05),
                'time_horizon': '1-3 days',
                'risk_reward_ratio': abs(return_pred) / 0.05  # Assuming 5% risk
            }
            
        except Exception as e:
            logger.error(f"Error generating suggestions: {str(e)}")
            return {'action': 'HOLD', 'confidence': 0, 'reasoning': ['Analysis error']}
    
    async def _calculate_profit_potential(self, symbol: str, suggestions: Dict, df: pd.DataFrame) -> Dict:
        """Calculate detailed profit potential analysis"""
        try:
            current_price = suggestions.get('entry_price', df['Close'].iloc[-1])
            target_price = suggestions.get('target_price', current_price)
            action = suggestions.get('action', 'HOLD')
            
            # Calculate potential profits for different position sizes
            position_sizes = [100, 500, 1000, 5000, 10000]  # Dollar amounts
            
            profit_scenarios = []
            
            for position_size in position_sizes:
                shares = position_size / current_price
                
                if action in ['BUY', 'STRONG_BUY']:
                    potential_profit = (target_price - current_price) * shares
                    profit_percentage = (target_price - current_price) / current_price * 100
                elif action in ['SELL', 'STRONG_SELL']:
                    potential_profit = (current_price - target_price) * shares
                    profit_percentage = (current_price - target_price) / current_price * 100
                else:
                    potential_profit = 0
                    profit_percentage = 0
                
                profit_scenarios.append({
                    'investment': position_size,
                    'shares': round(shares, 2),
                    'potential_profit': round(potential_profit, 2),
                    'profit_percentage': round(profit_percentage, 2),
                    'total_value': round(position_size + potential_profit, 2)
                })
            
            return {
                'scenarios': profit_scenarios,
                'best_case_return': max([s['profit_percentage'] for s in profit_scenarios]),
                'risk_level': suggestions.get('confidence', 0),
                'time_to_target': '1-3 days',
                'probability_of_profit': suggestions.get('confidence', 0) * 100
            }
            
        except Exception as e:
            logger.error(f"Error calculating profit potential: {str(e)}")
            return {'scenarios': [], 'best_case_return': 0}
    
    def _assess_risk_level(self, trend_analysis: Dict, predictions: Dict) -> str:
        """Assess overall risk level"""
        try:
            confidence = predictions.get('1_day', {}).get('confidence', 0)
            trend_strength = trend_analysis.get('trend_strength', 0)
            
            if confidence > 0.8 and trend_strength > 0.02:
                return 'LOW'
            elif confidence > 0.6 and trend_strength > 0.01:
                return 'MEDIUM'
            elif confidence > 0.4:
                return 'HIGH'
            else:
                return 'VERY_HIGH'
                
        except Exception:
            return 'UNKNOWN'
    
    def _determine_market_sentiment(self, trend_analysis: Dict) -> str:
        """Determine overall market sentiment"""
        try:
            overall_trend = trend_analysis.get('overall_trend', 'NEUTRAL')
            short_trend = trend_analysis.get('short_term_trend', {}).get('percentage', 0)
            
            if overall_trend == 'BULLISH' and short_trend > 2:
                return 'VERY_BULLISH'
            elif overall_trend == 'BULLISH':
                return 'BULLISH'
            elif overall_trend == 'BEARISH' and short_trend < -2:
                return 'VERY_BEARISH'
            elif overall_trend == 'BEARISH':
                return 'BEARISH'
            else:
                return 'NEUTRAL'
                
        except Exception:
            return 'UNKNOWN'
    
    def _determine_overall_trend(self, short: float, medium: float, long: float) -> str:
        """Determine overall trend from multiple timeframes"""
        trends = [short, medium, long]
        positive = sum(1 for t in trends if t > 1)
        negative = sum(1 for t in trends if t < -1)
        
        if positive >= 2:
            return 'BULLISH'
        elif negative >= 2:
            return 'BEARISH'
        else:
            return 'NEUTRAL'
    
    def _get_key_indicators(self, features_df: pd.DataFrame) -> Dict:
        """Get key technical indicators"""
        try:
            latest = features_df.iloc[-1]
            
            return {
                'rsi': float(latest.get('RSI', 50)),
                'macd': float(latest.get('MACD', 0)),
                'sma_20': float(latest.get('SMA_20', 0)),
                'sma_50': float(latest.get('SMA_50', 0)),
                'volume_ratio': float(latest.get('volume_sma_ratio', 1)),
                'volatility': float(latest.get('volatility_20', 0))
            }
            
        except Exception:
            return {}
    
    def _create_error_response(self, symbol: str, error: str) -> Dict:
        """Create error response"""
        return {
            'symbol': symbol,
            'timestamp': datetime.now().isoformat(),
            'error': error,
            'suggestions': {'action': 'HOLD', 'confidence': 0, 'reasoning': ['Analysis unavailable']},
            'profit_potential': {'scenarios': [], 'best_case_return': 0}
        }
