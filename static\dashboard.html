<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Trading Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: #fff;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .status-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 30px;
            backdrop-filter: blur(10px);
        }
        
        .status-item {
            text-align: center;
        }
        
        .status-value {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .status-label {
            font-size: 0.9rem;
            opacity: 0.8;
        }
        
        .dashboard-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .card {
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }
        
        .card h3 {
            margin-bottom: 15px;
            color: #ffd700;
            border-bottom: 2px solid #ffd700;
            padding-bottom: 10px;
        }
        
        .signals-list {
            max-height: 300px;
            overflow-y: auto;
        }
        
        .signal-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            margin-bottom: 10px;
            background: rgba(255,255,255,0.05);
            border-radius: 8px;
            border-left: 4px solid;
        }
        
        .signal-buy {
            border-left-color: #00ff88;
        }
        
        .signal-sell {
            border-left-color: #ff4444;
        }
        
        .signal-neutral {
            border-left-color: #ffaa00;
        }
        
        .symbol {
            font-weight: bold;
            font-size: 1.1rem;
        }
        
        .signal-type {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: bold;
        }
        
        .buy {
            background: #00ff88;
            color: #000;
        }
        
        .sell {
            background: #ff4444;
            color: #fff;
        }
        
        .neutral {
            background: #ffaa00;
            color: #000;
        }
        
        .opportunities-list {
            max-height: 300px;
            overflow-y: auto;
        }
        
        .opportunity-item {
            padding: 15px;
            margin-bottom: 10px;
            background: rgba(255,255,255,0.05);
            border-radius: 8px;
            border-left: 4px solid #00ff88;
        }
        
        .opportunity-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .opportunity-score {
            background: #ffd700;
            color: #000;
            padding: 4px 8px;
            border-radius: 4px;
            font-weight: bold;
        }
        
        .opportunity-reasons {
            font-size: 0.9rem;
            opacity: 0.8;
        }
        
        .controls {
            display: flex;
            gap: 15px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
        }
        
        .btn-primary {
            background: linear-gradient(45deg, #00ff88, #00cc6a);
            color: #000;
        }
        
        .btn-secondary {
            background: linear-gradient(45deg, #ffd700, #ffb700);
            color: #000;
        }
        
        .btn-danger {
            background: linear-gradient(45deg, #ff4444, #cc0000);
            color: #fff;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }
        
        .input-group {
            display: flex;
            gap: 10px;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .input-group input, .input-group select {
            padding: 8px 12px;
            border: 1px solid rgba(255,255,255,0.3);
            border-radius: 5px;
            background: rgba(255,255,255,0.1);
            color: #fff;
            font-size: 1rem;
        }
        
        .input-group input::placeholder {
            color: rgba(255,255,255,0.6);
        }
        
        .log-container {
            background: rgba(0,0,0,0.3);
            border-radius: 10px;
            padding: 15px;
            max-height: 200px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
        }
        
        .log-entry {
            margin-bottom: 5px;
            padding: 5px;
            border-radius: 3px;
        }
        
        .log-info {
            color: #00ff88;
        }
        
        .log-warning {
            color: #ffaa00;
        }
        
        .log-error {
            color: #ff4444;
        }
        
        .connection-status {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 10px 15px;
            border-radius: 5px;
            font-weight: bold;
        }
        
        .connected {
            background: #00ff88;
            color: #000;
        }
        
        .disconnected {
            background: #ff4444;
            color: #fff;
        }
        
        @media (max-width: 768px) {
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
            
            .controls {
                flex-direction: column;
            }
            
            .status-bar {
                flex-direction: column;
                gap: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="connection-status" id="connectionStatus">Disconnected</div>
    
    <div class="container">
        <div class="header">
            <h1>🤖 AI Trading Dashboard</h1>
            <p>Real-time market analysis and trading signals</p>
        </div>
        
        <div class="status-bar">
            <div class="status-item">
                <div class="status-value" id="marketStatus">CLOSED</div>
                <div class="status-label">Market Status</div>
            </div>
            <div class="status-item">
                <div class="status-value" id="monitoredSymbols">0</div>
                <div class="status-label">Monitored Symbols</div>
            </div>
            <div class="status-item">
                <div class="status-value" id="activeSignals">0</div>
                <div class="status-label">Active Signals</div>
            </div>
            <div class="status-item">
                <div class="status-value" id="lastUpdate">--:--</div>
                <div class="status-label">Last Update</div>
            </div>
        </div>
        
        <div class="controls">
            <button class="btn btn-primary" onclick="startMonitoring()">Start Monitoring</button>
            <button class="btn btn-secondary" onclick="scanMarket()">Scan Market</button>
            <button class="btn btn-danger" onclick="stopMonitoring()">Stop Monitoring</button>

            <div class="input-group">
                <input type="text" id="symbolInput" placeholder="Enter symbols (e.g., AAPL,GOOGL,MSFT)">
                <button class="btn btn-primary" onclick="addSymbols()">Add Symbols</button>
            </div>

            <div class="input-group">
                <input type="text" id="searchInput" placeholder="Search companies (e.g., Apple, TSLA, Microsoft)">
                <button class="btn btn-primary" onclick="searchCompanies()">Search</button>
            </div>

            <div class="input-group">
                <input type="text" id="aiAnalysisInput" placeholder="Enter symbol for AI analysis">
                <button class="btn btn-secondary" onclick="requestAIAnalysis()">AI Analysis</button>
            </div>

            <div class="input-group">
                <select id="scanType">
                    <option value="breakout">Breakout</option>
                    <option value="oversold">Oversold</option>
                    <option value="momentum">Momentum</option>
                    <option value="gap">Gap</option>
                    <option value="volume">Volume</option>
                </select>
                <button class="btn btn-secondary" onclick="performScan()">Scan</button>
            </div>
        </div>
        
        <div class="dashboard-grid">
            <div class="card">
                <h3>📊 Live Signals</h3>
                <div class="signals-list" id="signalsList">
                    <p>No signals available. Start monitoring to see live signals.</p>
                </div>
            </div>

            <div class="card">
                <h3>🎯 Market Opportunities</h3>
                <div class="opportunities-list" id="opportunitiesList">
                    <p>Run a market scan to find trading opportunities.</p>
                </div>
            </div>
        </div>

        <div class="dashboard-grid">
            <div class="card">
                <h3>🤖 AI Analysis Results</h3>
                <div class="opportunities-list" id="aiAnalysisList">
                    <p>Request AI analysis for detailed predictions and profit potential.</p>
                </div>
            </div>

            <div class="card">
                <h3>🔔 Notifications</h3>
                <div class="signals-list" id="notificationsList">
                    <p>Notifications will appear here.</p>
                </div>
            </div>
        </div>

        <div class="card">
            <h3>🔍 Company Search Results</h3>
            <div class="opportunities-list" id="searchResultsList">
                <p>Search for companies to see detailed information.</p>
            </div>
        </div>
        
        <div class="card">
            <h3>📝 Activity Log</h3>
            <div class="log-container" id="logContainer">
                <div class="log-entry log-info">System initialized. Ready to start trading analysis.</div>
            </div>
        </div>
    </div>

    <script>
        let ws = null;
        let isConnected = false;
        
        // Initialize WebSocket connection
        function initWebSocket() {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}/ws/live`;
            
            ws = new WebSocket(wsUrl);
            
            ws.onopen = function(event) {
                isConnected = true;
                updateConnectionStatus();
                addLog('Connected to live trading engine', 'info');
            };
            
            ws.onmessage = function(event) {
                const data = JSON.parse(event.data);
                handleWebSocketMessage(data);
            };
            
            ws.onclose = function(event) {
                isConnected = false;
                updateConnectionStatus();
                addLog('Disconnected from trading engine', 'warning');
                
                // Attempt to reconnect after 5 seconds
                setTimeout(initWebSocket, 5000);
            };
            
            ws.onerror = function(error) {
                addLog('WebSocket error: ' + error, 'error');
            };
        }
        
        function handleWebSocketMessage(data) {
            switch(data.type) {
                case 'connection_established':
                    updateMarketStatus(data.market_status);
                    updateSignals(data.current_signals);
                    if (data.notifications) {
                        updateNotifications(data.notifications);
                    }
                    break;

                case 'market_update':
                    updateSignals(data.data);
                    updateLastUpdate();
                    break;

                case 'signal_change':
                    addLog(`Signal change: ${data.symbol} → ${data.signal}`, 'info');
                    break;

                case 'price_alert':
                    addLog(`Price Alert: ${data.message}`, 'warning');
                    break;

                case 'notification':
                    handleNotification(data.notification);
                    break;

                case 'ai_analysis_result':
                    displayAIAnalysis(data.analysis);
                    addLog(`AI analysis completed for ${data.symbol}`, 'info');
                    break;

                case 'search_results':
                    displaySearchResults(data.results);
                    addLog(`Found ${data.results.length} companies for "${data.query}"`, 'info');
                    break;
            }
        }
        
        function updateConnectionStatus() {
            const statusEl = document.getElementById('connectionStatus');
            if (isConnected) {
                statusEl.textContent = 'Connected';
                statusEl.className = 'connection-status connected';
            } else {
                statusEl.textContent = 'Disconnected';
                statusEl.className = 'connection-status disconnected';
            }
        }
        
        function updateMarketStatus(marketStatus) {
            document.getElementById('marketStatus').textContent = marketStatus.status;
        }
        
        function updateSignals(signals) {
            const signalsList = document.getElementById('signalsList');
            
            if (!signals || Object.keys(signals).length === 0) {
                signalsList.innerHTML = '<p>No signals available.</p>';
                document.getElementById('activeSignals').textContent = '0';
                return;
            }
            
            let html = '';
            let signalCount = 0;
            
            for (const [symbol, signalData] of Object.entries(signals)) {
                const signal = typeof signalData === 'string' ? signalData : signalData.signal || 'NEUTRAL';
                const price = signalData.price || '--';
                
                const signalClass = signal.includes('BUY') ? 'signal-buy' : 
                                  signal.includes('SELL') ? 'signal-sell' : 'signal-neutral';
                
                const signalTypeClass = signal.includes('BUY') ? 'buy' : 
                                       signal.includes('SELL') ? 'sell' : 'neutral';
                
                html += `
                    <div class="signal-item ${signalClass}">
                        <div>
                            <div class="symbol">${symbol}</div>
                            <div style="font-size: 0.9rem; opacity: 0.8;">$${price}</div>
                        </div>
                        <div class="signal-type ${signalTypeClass}">${signal}</div>
                    </div>
                `;
                signalCount++;
            }
            
            signalsList.innerHTML = html;
            document.getElementById('activeSignals').textContent = signalCount;
            document.getElementById('monitoredSymbols').textContent = signalCount;
        }
        
        function updateLastUpdate() {
            const now = new Date();
            const timeString = now.toLocaleTimeString();
            document.getElementById('lastUpdate').textContent = timeString;
        }
        
        function addLog(message, type = 'info') {
            const logContainer = document.getElementById('logContainer');
            const timestamp = new Date().toLocaleTimeString();
            
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.textContent = `[${timestamp}] ${message}`;
            
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
            
            // Keep only last 50 log entries
            while (logContainer.children.length > 50) {
                logContainer.removeChild(logContainer.firstChild);
            }
        }
        
        async function startMonitoring() {
            const symbols = ['AAPL', 'GOOGL', 'MSFT', 'TSLA', 'AMZN', 'NVDA', 'META'];
            
            try {
                const response = await fetch('/live/monitor', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        symbols: symbols,
                        enable_alerts: true
                    })
                });
                
                if (response.ok) {
                    addLog('Started live monitoring for default symbols', 'info');
                } else {
                    addLog('Failed to start monitoring', 'error');
                }
            } catch (error) {
                addLog('Error starting monitoring: ' + error, 'error');
            }
        }
        
        async function scanMarket() {
            const scanType = document.getElementById('scanType').value;
            
            try {
                const response = await fetch('/scan', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        scan_type: scanType,
                        limit: 10
                    })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    updateOpportunities(data.opportunities);
                    addLog(`Market scan completed: ${data.opportunities_found} opportunities found`, 'info');
                } else {
                    addLog('Market scan failed', 'error');
                }
            } catch (error) {
                addLog('Error scanning market: ' + error, 'error');
            }
        }
        
        function updateOpportunities(opportunities) {
            const opportunitiesList = document.getElementById('opportunitiesList');
            
            if (!opportunities || opportunities.length === 0) {
                opportunitiesList.innerHTML = '<p>No opportunities found.</p>';
                return;
            }
            
            let html = '';
            
            opportunities.forEach(opp => {
                html += `
                    <div class="opportunity-item">
                        <div class="opportunity-header">
                            <div class="symbol">${opp.symbol}</div>
                            <div class="opportunity-score">${opp.score}</div>
                        </div>
                        <div style="margin-bottom: 5px;">$${opp.current_price?.toFixed(2) || '--'}</div>
                        <div class="opportunity-reasons">${opp.reasons?.join(', ') || 'No details available'}</div>
                    </div>
                `;
            });
            
            opportunitiesList.innerHTML = html;
        }
        
        function addSymbols() {
            const input = document.getElementById('symbolInput');
            const symbols = input.value.toUpperCase().split(',').map(s => s.trim()).filter(s => s);
            
            if (symbols.length === 0) {
                addLog('Please enter valid symbols', 'warning');
                return;
            }
            
            // Send symbols to monitoring (simplified - would need proper API call)
            addLog(`Added symbols for monitoring: ${symbols.join(', ')}`, 'info');
            input.value = '';
        }
        
        function stopMonitoring() {
            addLog('Monitoring stopped', 'warning');
        }
        
        function performScan() {
            scanMarket();
        }

        // New functions for enhanced features

        function searchCompanies() {
            const query = document.getElementById('searchInput').value.trim();
            if (!query) {
                addLog('Please enter a search query', 'warning');
                return;
            }

            if (ws && ws.readyState === WebSocket.OPEN) {
                ws.send(JSON.stringify({
                    type: 'search_company',
                    query: query
                }));
                addLog(`Searching for: ${query}`, 'info');
            } else {
                // Fallback to HTTP API
                searchCompaniesHTTP(query);
            }

            document.getElementById('searchInput').value = '';
        }

        async function searchCompaniesHTTP(query) {
            try {
                const response = await fetch('/search/companies', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        query: query,
                        limit: 10
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    displaySearchResults(data.companies);
                    addLog(`Found ${data.results_count} companies`, 'info');
                } else {
                    addLog('Company search failed', 'error');
                }
            } catch (error) {
                addLog('Error searching companies: ' + error, 'error');
            }
        }

        function displaySearchResults(results) {
            const searchResultsList = document.getElementById('searchResultsList');

            if (!results || results.length === 0) {
                searchResultsList.innerHTML = '<p>No companies found.</p>';
                return;
            }

            let html = '';

            results.forEach(company => {
                const price = company.current_price ? `$${company.current_price.toFixed(2)}` : 'N/A';
                const marketCap = company.market_cap ? formatMarketCap(company.market_cap) : 'N/A';

                html += `
                    <div class="opportunity-item" onclick="getCompanyDetails('${company.symbol}')">
                        <div class="opportunity-header">
                            <div>
                                <div class="symbol">${company.symbol}</div>
                                <div style="font-size: 0.9rem; opacity: 0.8;">${company.name}</div>
                            </div>
                            <div>
                                <div style="font-weight: bold;">${price}</div>
                                <div style="font-size: 0.8rem; opacity: 0.7;">${marketCap}</div>
                            </div>
                        </div>
                        <div class="opportunity-reasons">
                            ${company.sector || 'Unknown Sector'} • ${company.exchange || 'Unknown Exchange'}
                        </div>
                    </div>
                `;
            });

            searchResultsList.innerHTML = html;
        }

        function requestAIAnalysis() {
            const symbol = document.getElementById('aiAnalysisInput').value.trim().toUpperCase();
            if (!symbol) {
                addLog('Please enter a symbol for AI analysis', 'warning');
                return;
            }

            if (ws && ws.readyState === WebSocket.OPEN) {
                ws.send(JSON.stringify({
                    type: 'request_ai_analysis',
                    symbol: symbol
                }));
                addLog(`Requesting AI analysis for ${symbol}`, 'info');
            } else {
                // Fallback to HTTP API
                requestAIAnalysisHTTP(symbol);
            }

            document.getElementById('aiAnalysisInput').value = '';
        }

        async function requestAIAnalysisHTTP(symbol) {
            try {
                const response = await fetch('/ai/analyze', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        symbol: symbol,
                        timeframe: '1d',
                        include_notifications: true
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    displayAIAnalysis(data.analysis);
                    addLog(`AI analysis completed for ${symbol}`, 'info');
                } else {
                    addLog('AI analysis failed', 'error');
                }
            } catch (error) {
                addLog('Error requesting AI analysis: ' + error, 'error');
            }
        }

        function displayAIAnalysis(analysis) {
            const aiAnalysisList = document.getElementById('aiAnalysisList');

            if (!analysis || analysis.error) {
                aiAnalysisList.innerHTML = '<p>AI analysis failed or unavailable.</p>';
                return;
            }

            const suggestions = analysis.suggestions || {};
            const profitPotential = analysis.profit_potential || {};
            const scenarios = profitPotential.scenarios || [];

            let html = `
                <div class="opportunity-item">
                    <div class="opportunity-header">
                        <div>
                            <div class="symbol">${analysis.symbol}</div>
                            <div style="font-size: 0.9rem; opacity: 0.8;">AI Recommendation</div>
                        </div>
                        <div class="opportunity-score">${suggestions.action || 'HOLD'}</div>
                    </div>
                    <div style="margin: 10px 0;">
                        <strong>Confidence:</strong> ${((suggestions.confidence || 0) * 100).toFixed(0)}%<br>
                        <strong>Current Price:</strong> $${(analysis.current_price || 0).toFixed(2)}<br>
                        <strong>Target Price:</strong> $${(suggestions.target_price || 0).toFixed(2)}<br>
                        <strong>Risk Level:</strong> ${analysis.risk_level || 'Unknown'}
                    </div>
            `;

            if (scenarios.length > 0) {
                const bestScenario = scenarios[scenarios.length - 1];
                html += `
                    <div style="background: rgba(0,255,136,0.1); padding: 10px; border-radius: 5px; margin: 10px 0;">
                        <strong>💰 Profit Potential (${bestScenario.investment} investment):</strong><br>
                        Potential Profit: $${bestScenario.potential_profit.toFixed(2)} (${bestScenario.profit_percentage.toFixed(1)}%)<br>
                        Total Value: $${bestScenario.total_value.toFixed(2)}
                    </div>
                `;
            }

            if (suggestions.reasoning && suggestions.reasoning.length > 0) {
                html += `
                    <div class="opportunity-reasons">
                        <strong>Reasoning:</strong> ${suggestions.reasoning.join(', ')}
                    </div>
                `;
            }

            html += '</div>';

            aiAnalysisList.innerHTML = html;
        }

        function handleNotification(notification) {
            // Add to notifications list
            updateNotifications([notification]);

            // Show popup notification
            showPopupNotification(notification);

            // Add to log
            addLog(`Notification: ${notification.title}`, 'info');
        }

        function updateNotifications(notifications) {
            const notificationsList = document.getElementById('notificationsList');

            if (!notifications || notifications.length === 0) {
                notificationsList.innerHTML = '<p>No notifications.</p>';
                return;
            }

            let html = '';

            notifications.slice(0, 5).forEach(notif => {
                const priorityClass = notif.priority === 'CRITICAL' ? 'signal-sell' :
                                    notif.priority === 'HIGH' ? 'signal-buy' : 'signal-neutral';

                html += `
                    <div class="signal-item ${priorityClass}">
                        <div>
                            <div class="symbol">${notif.symbol}</div>
                            <div style="font-size: 0.8rem; opacity: 0.8;">${notif.message}</div>
                        </div>
                        <div class="signal-type ${notif.priority.toLowerCase()}">${notif.priority}</div>
                    </div>
                `;
            });

            notificationsList.innerHTML = html;
        }

        function showPopupNotification(notification) {
            // Create popup notification (simplified)
            if (notification.type === 'PROFIT_ALERT') {
                const message = `${notification.title}\n${notification.message}`;

                // In a real app, you'd use a proper notification library
                if ('Notification' in window && Notification.permission === 'granted') {
                    new Notification(notification.title, {
                        body: notification.message,
                        icon: '/static/icon.png'
                    });
                } else {
                    // Fallback to alert (in production, use a toast library)
                    setTimeout(() => alert(message), 100);
                }
            }
        }

        async function getCompanyDetails(symbol) {
            try {
                const response = await fetch(`/company/${symbol}`);

                if (response.ok) {
                    const data = await response.json();
                    displayCompanyDetails(data.company_data);
                } else {
                    addLog(`Failed to get details for ${symbol}`, 'error');
                }
            } catch (error) {
                addLog(`Error getting company details: ${error}`, 'error');
            }
        }

        function displayCompanyDetails(companyData) {
            // This would open a modal or new section with detailed company info
            // For now, just log the data
            addLog(`Company details loaded for ${companyData.symbol}`, 'info');
            console.log('Company Details:', companyData);
        }

        function formatMarketCap(marketCap) {
            if (marketCap >= 1e12) {
                return `$${(marketCap / 1e12).toFixed(1)}T`;
            } else if (marketCap >= 1e9) {
                return `$${(marketCap / 1e9).toFixed(1)}B`;
            } else if (marketCap >= 1e6) {
                return `$${(marketCap / 1e6).toFixed(1)}M`;
            } else {
                return `$${marketCap.toFixed(0)}`;
            }
        }

        // Request notification permission on load
        function requestNotificationPermission() {
            if ('Notification' in window && Notification.permission === 'default') {
                Notification.requestPermission();
            }
        }

        // Initialize the dashboard
        document.addEventListener('DOMContentLoaded', function() {
            initWebSocket();
            requestNotificationPermission();
            addLog('Enhanced AI Trading Dashboard initialized', 'info');
        });
    </script>
</body>
</html>
