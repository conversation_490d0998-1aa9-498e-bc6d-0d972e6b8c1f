"""
Data Collection Module for fetching live and historical trading data
"""
import yfinance as yf
import pandas as pd
import numpy as np
from alpha_vantage.timeseries import TimeSeries
from alpha_vantage.fundamentaldata import FundamentalData
import finnhub
import asyncio
import aiohttp
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from loguru import logger
from config import Config

class DataCollector:
    def __init__(self):
        self.config = Config()
        self.alpha_vantage_ts = TimeSeries(key=self.config.ALPHA_VANTAGE_API_KEY, output_format='pandas')
        self.alpha_vantage_fd = FundamentalData(key=self.config.ALPHA_VANTAGE_API_KEY, output_format='pandas')
        self.finnhub_client = finnhub.Client(api_key=self.config.FINNHUB_API_KEY)
        
    def get_historical_data(self, symbol: str, period: str = "2y", interval: str = "1d") -> pd.DataFrame:
        """
        Fetch historical data for a given symbol
        
        Args:
            symbol: Stock symbol (e.g., 'AAPL')
            period: Data period (1d, 5d, 1mo, 3mo, 6mo, 1y, 2y, 5y, 10y, ytd, max)
            interval: Data interval (1m, 2m, 5m, 15m, 30m, 60m, 90m, 1h, 1d, 5d, 1wk, 1mo, 3mo)
        
        Returns:
            DataFrame with OHLCV data
        """
        try:
            ticker = yf.Ticker(symbol)
            data = ticker.history(period=period, interval=interval)
            
            if data.empty:
                logger.warning(f"No data found for symbol {symbol}")
                return pd.DataFrame()
            
            # Standardize column names
            data.columns = ['Open', 'High', 'Low', 'Close', 'Volume']
            data['Symbol'] = symbol
            data.reset_index(inplace=True)
            
            logger.info(f"Successfully fetched {len(data)} records for {symbol}")
            return data
            
        except Exception as e:
            logger.error(f"Error fetching historical data for {symbol}: {str(e)}")
            return pd.DataFrame()
    
    def get_real_time_data(self, symbol: str) -> Dict:
        """
        Fetch real-time data for a given symbol
        
        Args:
            symbol: Stock symbol
            
        Returns:
            Dictionary with current price data
        """
        try:
            ticker = yf.Ticker(symbol)
            info = ticker.info
            
            # Get current price data
            current_data = {
                'symbol': symbol,
                'current_price': info.get('currentPrice', 0),
                'previous_close': info.get('previousClose', 0),
                'open': info.get('open', 0),
                'day_high': info.get('dayHigh', 0),
                'day_low': info.get('dayLow', 0),
                'volume': info.get('volume', 0),
                'market_cap': info.get('marketCap', 0),
                'pe_ratio': info.get('trailingPE', 0),
                'timestamp': datetime.now()
            }
            
            return current_data
            
        except Exception as e:
            logger.error(f"Error fetching real-time data for {symbol}: {str(e)}")
            return {}
    
    def get_company_fundamentals(self, symbol: str) -> Dict:
        """
        Fetch company fundamental data
        
        Args:
            symbol: Stock symbol
            
        Returns:
            Dictionary with fundamental data
        """
        try:
            ticker = yf.Ticker(symbol)
            info = ticker.info
            
            fundamentals = {
                'symbol': symbol,
                'company_name': info.get('longName', ''),
                'sector': info.get('sector', ''),
                'industry': info.get('industry', ''),
                'market_cap': info.get('marketCap', 0),
                'enterprise_value': info.get('enterpriseValue', 0),
                'pe_ratio': info.get('trailingPE', 0),
                'forward_pe': info.get('forwardPE', 0),
                'peg_ratio': info.get('pegRatio', 0),
                'price_to_book': info.get('priceToBook', 0),
                'debt_to_equity': info.get('debtToEquity', 0),
                'return_on_equity': info.get('returnOnEquity', 0),
                'revenue_growth': info.get('revenueGrowth', 0),
                'earnings_growth': info.get('earningsGrowth', 0),
                'profit_margins': info.get('profitMargins', 0),
                'operating_margins': info.get('operatingMargins', 0),
                'dividend_yield': info.get('dividendYield', 0),
                'beta': info.get('beta', 0),
                'fifty_two_week_high': info.get('fiftyTwoWeekHigh', 0),
                'fifty_two_week_low': info.get('fiftyTwoWeekLow', 0),
                'average_volume': info.get('averageVolume', 0),
                'shares_outstanding': info.get('sharesOutstanding', 0)
            }
            
            return fundamentals
            
        except Exception as e:
            logger.error(f"Error fetching fundamentals for {symbol}: {str(e)}")
            return {}
    
    async def get_multiple_symbols_data(self, symbols: List[str]) -> Dict[str, pd.DataFrame]:
        """
        Fetch historical data for multiple symbols asynchronously
        
        Args:
            symbols: List of stock symbols
            
        Returns:
            Dictionary mapping symbols to their DataFrames
        """
        results = {}
        
        for symbol in symbols:
            try:
                data = self.get_historical_data(symbol)
                if not data.empty:
                    results[symbol] = data
                    
                # Add small delay to avoid rate limiting
                await asyncio.sleep(0.1)
                
            except Exception as e:
                logger.error(f"Error fetching data for {symbol}: {str(e)}")
                continue
        
        return results
    
    def get_market_news(self, symbol: str, limit: int = 10) -> List[Dict]:
        """
        Fetch recent news for a symbol
        
        Args:
            symbol: Stock symbol
            limit: Number of news articles to fetch
            
        Returns:
            List of news articles
        """
        try:
            # Using Finnhub for news
            news = self.finnhub_client.company_news(symbol, 
                                                   _from=(datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d'),
                                                   to=datetime.now().strftime('%Y-%m-%d'))
            
            return news[:limit] if news else []
            
        except Exception as e:
            logger.error(f"Error fetching news for {symbol}: {str(e)}")
            return []
    
    def validate_symbol(self, symbol: str) -> bool:
        """
        Validate if a symbol exists and has sufficient data
        
        Args:
            symbol: Stock symbol to validate
            
        Returns:
            Boolean indicating if symbol is valid
        """
        try:
            ticker = yf.Ticker(symbol)
            info = ticker.info
            
            # Check if symbol exists and has basic data
            if not info or 'currentPrice' not in info:
                return False
            
            # Check market cap and volume requirements
            market_cap = info.get('marketCap', 0)
            avg_volume = info.get('averageVolume', 0)
            
            if market_cap < self.config.MIN_MARKET_CAP or avg_volume < self.config.MIN_TRADING_VOLUME:
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error validating symbol {symbol}: {str(e)}")
            return False
